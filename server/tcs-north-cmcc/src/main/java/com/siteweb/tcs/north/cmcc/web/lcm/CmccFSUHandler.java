package com.siteweb.tcs.north.cmcc.web.lcm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.enums.LifeCycleEventEnum;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.hub.dal.entity.TcsGateway;
import com.siteweb.tcs.hub.service.ITcsGatewayService;
import com.siteweb.tcs.north.cmcc.dal.dto.CmccSiteInfoDTO;
import com.siteweb.tcs.s6.access.dal.entity.DeviceMap;
import com.siteweb.tcs.s6.access.dal.entity.GatewayMap;
import com.siteweb.tcs.north.cmcc.dal.entity.CmccSiteMap;
import com.siteweb.tcs.s6.access.web.service.IGatewayMapService;
import com.siteweb.tcs.siteweb.dto.CreateMonitorUnitDTO;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * CMCC FSU处理器
 * 负责处理FSU相关的创建、更新、删除逻辑
 */
@Component
@Slf4j
public class CmccFSUHandler extends CmccBaseHandler {

    @Autowired
    private IGatewayMapService gatewayMapService;

    @Autowired
    private CmccDeviceHandler cmccDeviceHandler;

    @Autowired
    private ITcsGatewayService tcsGatewayService;

    @Autowired
    private CmccSiteHandler cmccSiteHandler;

    /**
     * 处理FSU信息
     */
    public Integer handleFSUInfo(GatewayConfigChangeDto gatewayConfigChangeDto,
                                 Map<String, CmccSiteMap> cmccSiteMapMap) {
        CmccSiteInfoDTO siteInfoDTO = cmccConfigParser.parseSiteInfo(gatewayConfigChangeDto);
        
        switch (gatewayConfigChangeDto.getLifeCycleEvent()) {
            case CREATE:
                return createFSU(gatewayConfigChangeDto, siteInfoDTO, cmccSiteMapMap);
            case DELETE:
                return deleteFSU(gatewayConfigChangeDto);
            case UPDATE:
                return updateFSU(gatewayConfigChangeDto, siteInfoDTO, cmccSiteMapMap);
            case NOT_CHANGE:
                GatewayMap gatewayMap = gatewayMapService.getByGatewayId(gatewayConfigChangeDto.getId());
                return gatewayMap.getNorthMonitorUnitId();
            default:
                return null;
        }
    }

    /**
     * 创建FSU
     */
    private Integer createFSU(GatewayConfigChangeDto gatewayConfigChangeDto,
                              CmccSiteInfoDTO siteInfoDTO,
                              Map<String, CmccSiteMap> cmccSiteMapMap) {
        CmccSiteMap cmccSiteMap = getSiteMap(siteInfoDTO.getSiteId(), cmccSiteMapMap);
        if (cmccSiteMap == null) {
            throw new IllegalStateException("站点映射不存在: " + siteInfoDTO.getSiteId());
        }

        CreateMonitorUnitDTO createMonitorUnitDTO = cmccConfigParser.parseFSUInfo(gatewayConfigChangeDto);
        MonitorUnitDTO monitorUnit = createMonitorUnitDTO.toMonitorUnit(cmccSiteMap.getStationId());
        try {
            sitewebPersistentService.executeTransaction(()->{
                sitewebPersistentService.getConfigAPI().createV3ForMonitorUnit(monitorUnit);
            });
        }catch (Exception ex){
            log.error("创建监控单元失败", ex);
            return null;
        }
        // 保存网关映射
        saveGatewayMap(gatewayConfigChangeDto.getPluginId(),gatewayConfigChangeDto.getId(), monitorUnit.getMonitorUnitId(),cmccSiteMap.getStationId());

        return monitorUnit.getMonitorUnitId();
    }

    /**
     * 删除FSU
     */
    private Integer deleteFSU(GatewayConfigChangeDto gatewayConfigChangeDto) {
        // TODO: 实现删除逻辑
        GatewayMap gatewayMap = gatewayMapService.getByGatewayId(gatewayConfigChangeDto.getId());
        if(ObjectUtil.isEmpty(gatewayMap)) return null;
        sitewebPersistentService.getConfigAPI().deleteForMonitorUnit(gatewayMap.getNorthMonitorUnitId(),true);
        //删除DeviceMap，模板
        cmccDeviceHandler.handleDeleteByGatewayId(gatewayConfigChangeDto.getId());
        //删除GatewayMap
        gatewayMapService.deleteByGatewayId(gatewayConfigChangeDto.getId());
        return gatewayMap.getNorthMonitorUnitId();
    }

    /**
     * 更新FSU
     */
    private Integer updateFSU(GatewayConfigChangeDto gatewayConfigChangeDto,
                              CmccSiteInfoDTO siteInfoDTO,
                              Map<String, CmccSiteMap> cmccSiteMapMap) {
        // TODO: 实现更新逻辑
        // 查出GatewayMap
        CmccSiteMap cmccSiteMap = getSiteMap(siteInfoDTO.getSiteId(), cmccSiteMapMap);
        if(ObjectUtil.isEmpty(cmccSiteMap)){
            cmccSiteHandler.handleSiteInfo(gatewayConfigChangeDto, cmccSiteMapMap);
            cmccSiteMap = getSiteMap(siteInfoDTO.getSiteId(), cmccSiteMapMap);
        }
        GatewayMap gatewayMap = gatewayMapService.getByGatewayId(gatewayConfigChangeDto.getId());
        if(ObjectUtil.isEmpty(gatewayMap)) return null;
        CreateMonitorUnitDTO createMonitorUnitDTO = cmccConfigParser.parseFSUInfo(gatewayConfigChangeDto);
        MonitorUnitDTO monitorUnit = createMonitorUnitDTO.toMonitorUnit(cmccSiteMap.getStationId());
        monitorUnit.setMonitorUnitId(gatewayMap.getNorthMonitorUnitId());
        sitewebPersistentService.getConfigAPI().updateForMonitorUnit(monitorUnit);
        return gatewayMap.getNorthMonitorUnitId();
    }

    /**
     * 保存网关映射
     */
    private void saveGatewayMap(String pluginId,Long gatewayId, Integer monitorUnitId,Integer stationId) {
        GatewayMap gatewayMap = new GatewayMap();
        gatewayMap.setGatewayId(gatewayId);
        gatewayMap.setNorthMonitorUnitId(monitorUnitId);
        gatewayMap.setNorthStationId(stationId);
        gatewayMap.setPluginId(pluginId);
        gatewayMapService.save(gatewayMap);
    }

    /**
     * 同步内容
     * @param gatewayIdList
     */
    public List<GatewayMap> handleGatewayConfigSyncByGatewayList(List<Long> gatewayIdList) {
        List<GatewayMap> gatewayMapList = gatewayMapService.getByGatewayIdList(gatewayIdList);
        List<TcsGateway> hubGatewayList = tcsGatewayService.getByGatewayIdList(gatewayIdList);
        Map<Long, TcsGateway> hubGatewayMap = hubGatewayList.stream()
                .collect(Collectors.toMap(TcsGateway::getId, Function.identity()));
        Set<Long> hubGatewayIds = hubGatewayMap.keySet();
        Set<Long> northGatewayIds = gatewayMapList.stream()
                .map(GatewayMap::getGatewayId)
                .collect(Collectors.toSet());
        // 找出北向有但hub同步列表中没有的网关(需要删除)
        Set<Long> gatewaysToDelete = new HashSet<>(northGatewayIds);
        gatewaysToDelete.removeAll(hubGatewayIds);

        // 找出两边都存在的网关(需要同步子组件)
        Set<Long> gatewaysToSync = new HashSet<>(hubGatewayIds);
        gatewaysToSync.retainAll(northGatewayIds);

        //删除
        if(CollectionUtil.isNotEmpty(gatewaysToDelete)){
            for (Long id : gatewaysToDelete) {
                TcsGateway tcsGateway = hubGatewayMap.get(id);
                GatewayConfigChangeDto gatewayConfigChangeDto = new GatewayConfigChangeDto();
                BeanUtils.copyProperties(tcsGateway,gatewayConfigChangeDto);
                gatewayConfigChangeDto.setLifeCycleEvent(LifeCycleEventEnum.DELETE);
                handleFSUInfo(gatewayConfigChangeDto,Map.of());
            }
            gatewayMapList = gatewayMapList.stream().filter(e -> gatewaysToDelete.contains(e.getGatewayId())).toList();
        }
        //更新
        if(CollectionUtil.isNotEmpty(gatewaysToSync)){
            for (Long id : gatewaysToSync) {
                TcsGateway tcsGateway = hubGatewayMap.get(id);
                GatewayConfigChangeDto gatewayConfigChangeDto = new GatewayConfigChangeDto();
                BeanUtils.copyProperties(tcsGateway,gatewayConfigChangeDto);
                gatewayConfigChangeDto.setLifeCycleEvent(LifeCycleEventEnum.UPDATE);
                handleFSUInfo(gatewayConfigChangeDto, MapUtil.newHashMap());
            }
        }
        return gatewayMapList;
    }
}
