package com.siteweb.tcs.north.cmcc.web.lcm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.tcs.common.enums.LifeCycleEventEnum;
import com.siteweb.tcs.hub.dal.dto.DeviceConfigChangeDto;
import com.siteweb.tcs.hub.dal.dto.DeviceConfigSyncDTO;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.service.ITcsDeviceService;
import com.siteweb.tcs.north.cmcc.dal.dto.CmccRoomInfoDTO;
import com.siteweb.tcs.north.cmcc.dal.dto.CmccSiteInfoDTO;
import com.siteweb.tcs.north.cmcc.dal.entity.*;
import com.siteweb.tcs.s6.access.dal.entity.DeviceMap;
import com.siteweb.tcs.s6.access.dal.entity.GatewayMap;
import com.siteweb.tcs.s6.access.web.service.IDeviceMapService;
import com.siteweb.tcs.north.cmcc.web.service.ICmccDeviceExtService;
import com.siteweb.tcs.north.cmcc.web.service.ICmccDeviceTypeMapService;
import com.siteweb.tcs.north.cmcc.web.service.ICmccDeviceTypeService;
import com.siteweb.tcs.north.cmcc.web.service.ICmccStandardMappingService;
import com.siteweb.tcs.siteweb.dto.CreateEquipmentDto;
import com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.Port;
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * CMCC设备处理器
 * 负责处理设备相关的创建、更新、删除逻辑
 */
@Component
@Slf4j
public class CmccDeviceHandler extends CmccBaseHandler {

    @Autowired
    private IDeviceMapService deviceMapService;

    @Autowired
    private ICmccDeviceTypeMapService cmccDeviceTypeMapService;

    @Autowired
    private ICmccDeviceTypeService cmccDeviceTypeService;

    @Autowired
    private ICmccStandardMappingService cmccStandardMappingService;

    @Autowired
    private CmccSiteHandler cmccSiteHandler;

    @Autowired
    private CmccRoomHandler cmccRoomHandler;

    @Autowired
    private CmccSignalHandler cmccSignalHandler;

    @Autowired
    private CmccAlarmHandler cmccAlarmHandler;

    @Autowired
    private CmccControlHandler cmccControlHandler;

    @Autowired
    private ICmccDeviceExtService cmccDeviceExtService;

    @Autowired
    private ITcsDeviceService tcsDeviceService;


    /**
     * 处理设备信息及其相关的信号、告警、控制
     * 统一处理设备生命周期，确保设备及其子组件的生命周期同步
     */
    public void handleDeviceInfo(GatewayConfigChangeDto gatewayConfigChangeDto,
                                 Map<String, CmccSiteMap> cmccSiteMapMap,
                                 Integer monitorUnitId) {
        Map<String, CmccRoomMap> cmccRoomMapMap = new HashMap<>();
        List<DeviceConfigChangeDto> configChangeDtoList = gatewayConfigChangeDto.getDevices();
        Long gatewayId = gatewayConfigChangeDto.getId();
        if(CollUtil.isEmpty(configChangeDtoList)) return;
        for (DeviceConfigChangeDto deviceConfigChangeDto : configChangeDtoList) {
            switch (deviceConfigChangeDto.getLifeCycleEvent()) {
                case CREATE:
                    handleDeviceCreate(gatewayId, deviceConfigChangeDto, cmccSiteMapMap, cmccRoomMapMap, monitorUnitId);
                    break;
                case DELETE:
                    handleDeviceDelete(deviceConfigChangeDto);
                    break;
                case UPDATE:
                    handleDeviceUpdate(deviceConfigChangeDto, cmccSiteMapMap, cmccRoomMapMap, monitorUnitId);
                    break;
                case NOT_CHANGE:
                    handleDeviceNoChange(deviceConfigChangeDto, cmccSiteMapMap, cmccRoomMapMap, monitorUnitId);
                    break;
            }
        }
    }

    private void handleDeviceNoChange(DeviceConfigChangeDto deviceConfigChangeDto, Map<String, CmccSiteMap> cmccSiteMapMap, Map<String, CmccRoomMap> cmccRoomMapMap, Integer monitorUnitId) {
        DeviceMap deviceMap = getDeviceMapByDeviceId(deviceConfigChangeDto.getId());
        handleDeviceSubComponents(deviceConfigChangeDto, null, deviceMap);
    }

    public void handleDeleteByGatewayId(Long gatewayId){
        //查设备模板id列表
        List<DeviceMap> deviceMapList = deviceMapService.getNorthEquipmentIdListByGatewayId(gatewayId);
        //删除设备模板
        Optional.ofNullable(deviceMapList)
                .ifPresent(list-> list.forEach(e -> {
                    sitewebPersistentService.getConfigAPI().deleteByIdForEquipmentTemplate(e.getNorthEquipmentTemplateId());
                    handleDeleteDeviceSubComponents(e.getDeviceId());
                }));
        //todo xsx 删除采集单元以及端口，清理彻底一些
        //删除deviceMap
        deviceMapService.deleteByGatewayId(gatewayId);
    }

    /**
     * 统一处理设备创建及其子组件（信号、告警、控制）
     */
    private void handleDeviceCreate(Long gatewayId,
                                    DeviceConfigChangeDto deviceConfigChangeDto,
                                    Map<String, CmccSiteMap> cmccSiteMapMap,
                                    Map<String, CmccRoomMap> cmccRoomMapMap,
                                    Integer monitorUnitId) {
        // 1. 创建设备
        Equipment equipment = createDevice(gatewayId, deviceConfigChangeDto,
                                         cmccSiteMapMap, cmccRoomMapMap, monitorUnitId);

        // 2. 获取设备映射信息（用于子组件处理）
        DeviceMap deviceMap = getDeviceMapByDeviceId(deviceConfigChangeDto.getId());

        // 3. 处理设备下的信号、告警、控制
        handleDeviceSubComponents(deviceConfigChangeDto, equipment, deviceMap);
    }

    /**
     * 统一处理设备删除及其子组件（信号、告警、控制）
     */
    private void handleDeviceDelete(DeviceConfigChangeDto deviceConfigChangeDto) {
        // 1. 获取设备映射信息
        DeviceMap deviceMap = getDeviceMapByDeviceId(deviceConfigChangeDto.getId());

        // 2. 根据设备删除SEC映射以及模板
        sitewebPersistentService.getConfigAPI().deleteByIdForEquipmentTemplate(deviceMap.getNorthEquipmentTemplateId());
        handleDeleteDeviceSubComponents(deviceConfigChangeDto.getId());

        // 3. 再删除设备
        sitewebPersistentService.getConfigAPI().deleteForEquipment(deviceMap.getNorthEquipmentId());

        // 4. 删除映射关系
        deviceMapService.deleteByDeviceId(deviceMap.getDeviceId());
    }

    /**
     * 统一处理设备更新及其子组件（信号、告警、控制）
     */
    private void handleDeviceUpdate(DeviceConfigChangeDto deviceConfigChangeDto,
                                    Map<String, CmccSiteMap> cmccSiteMapMap,
                                    Map<String, CmccRoomMap> cmccRoomMapMap,
                                    Integer monitorUnitId) {
        DeviceMap deviceMap = getDeviceMapByDeviceId(deviceConfigChangeDto.getId());

        // 1. 更新设备
        Equipment equipment = updateDevice(deviceConfigChangeDto, cmccSiteMapMap, cmccRoomMapMap, monitorUnitId, deviceMap);

        // 2. 获取设备映射信息

        // 3. 处理设备下的信号、告警、控制
        handleDeviceSubComponents(deviceConfigChangeDto, equipment, deviceMap);
    }

    /**
     * 处理设备子组件（信号、告警、控制）
     */
    private void handleDeviceSubComponents(DeviceConfigChangeDto deviceConfigChangeDto,
                                          Equipment equipment,
                                          DeviceMap deviceMap) {
        // 处理信号
        cmccSignalHandler.handleSignal(deviceConfigChangeDto, deviceMap);

        // 处理告警
        cmccAlarmHandler.handleAlarm(deviceConfigChangeDto, deviceMap);

        // 处理控制
        cmccControlHandler.handleControl(deviceConfigChangeDto, deviceMap);
    }

    //删除设备时删除sec
    private void handleDeleteDeviceSubComponents(Long deviceId) {
        // 删除信号
        cmccSignalHandler.handleDeleteByDeviceId(deviceId);
        // 删除告警
        cmccAlarmHandler.handleDeleteByDeviceId(deviceId);
        // 删除控制
        cmccControlHandler.handleDeleteByDeviceId(deviceId);
    }

    /**
     * 获取设备映射信息
     */
    private DeviceMap getDeviceMapByDeviceId(Long deviceId) {
        return deviceMapService.getByDeviceId(deviceId);
    }

    /**
     * 创建设备（原有方法）
     */
    private Equipment createDevice(Long gatewayId,
                                   DeviceConfigChangeDto deviceConfigChangeDto,
                                   Map<String, CmccSiteMap> cmccSiteMapMap,
                                   Map<String, CmccRoomMap> cmccRoomMapMap,
                                   Integer monitorUnitId) {
        // 处理站点
        CmccSiteInfoDTO cmccSiteInfoDTO = cmccConfigParser.parseSiteInfo(deviceConfigChangeDto);
        cmccSiteHandler.handleSiteInfo(cmccSiteInfoDTO, cmccSiteMapMap);
        CmccSiteMap cmccSiteMap = getSiteMap(cmccSiteInfoDTO.getSiteId(), cmccSiteMapMap);
        if(ObjectUtil.isEmpty(cmccSiteMap)){
            log.error("创建设备{}失败，失败原因是局站{}创建失败",deviceConfigChangeDto.getSouthDeviceName(),cmccSiteInfoDTO.getSiteName());
        }
        // 处理机房
        CmccRoomInfoDTO cmccRoomInfoDTO = cmccConfigParser.parseRoomInfo(deviceConfigChangeDto);
        cmccRoomHandler.handleRoomInfo(cmccRoomInfoDTO, cmccRoomMapMap, cmccSiteMap);
        CmccRoomMap cmccRoomMap = getRoomMap(cmccRoomInfoDTO.getUniqueKey(), cmccRoomMapMap);

        // 创建端口
        Port port = createPortInfo(deviceConfigChangeDto, monitorUnitId);

        // 创建采集单元
        SamplerUnit samplerUnit = createSamplerUnit(deviceConfigChangeDto, monitorUnitId, port);

        // 处理设备类型
        Integer equipmentCategoryId = handleDeviceType(deviceConfigChangeDto);

        // 创建设备
        Equipment equipment = createEquipment(deviceConfigChangeDto, cmccSiteMap, cmccRoomMap,
                                            monitorUnitId, port, samplerUnit, equipmentCategoryId);

        // 保存设备映射
        saveDeviceMap(gatewayId, deviceConfigChangeDto, monitorUnitId, equipment);

        // 保存CMCC设备拓展表
        saveCmccDeviceExt(deviceConfigChangeDto, equipment);

        return equipment;
    }

    /**
     * 更新设备
     */
    private Equipment updateDevice(DeviceConfigChangeDto deviceConfigChangeDto,
                                   Map<String, CmccSiteMap> cmccSiteMapMap,
                                   Map<String, CmccRoomMap> cmccRoomMapMap,
                                   Integer monitorUnitId,
                                   DeviceMap deviceMap) {
        // TODO: 实现更新逻辑
        Integer equipmentCategoryId = handleDeviceType(deviceConfigChangeDto);
        EquipmentDetailDTO equipmentDetailDTO = cmccConfigParser.parseUpdateDeviceInfo(deviceConfigChangeDto);
        equipmentDetailDTO.setEquipmentCategory(equipmentCategoryId);
        equipmentDetailDTO.setEquipmentId(deviceMap.getNorthEquipmentId());
        //局站
        CmccSiteInfoDTO cmccSiteInfoDTO = cmccConfigParser.parseSiteInfo(deviceConfigChangeDto);
        CmccSiteMap siteMap = getSiteMap(cmccSiteInfoDTO.getSiteId(), cmccSiteMapMap);
        equipmentDetailDTO.setStationId(siteMap.getStationId());
        //机房
        CmccRoomInfoDTO cmccRoomInfoDTO = cmccConfigParser.parseRoomInfo(deviceConfigChangeDto);
        CmccRoomMap roomMap = getRoomMap(cmccRoomInfoDTO.getRoomId(), cmccRoomMapMap);
        equipmentDetailDTO.setHouseId(roomMap.getHouseId());

        Equipment equipment = sitewebPersistentService.getConfigAPI().updateForEquipment(equipmentDetailDTO);
        return equipment;
    }

    /**
     * 创建端口信息
     */
    private Port createPortInfo(DeviceConfigChangeDto deviceConfigChangeDto, Integer monitorUnitId) {
        Integer maxPortByMonitorUnitId = sitewebPersistentService.getConfigAPI().getMaxPortByMonitorUnitId(monitorUnitId);
        Integer portNo = maxPortByMonitorUnitId + 1;
        String portName = String.format(PORT_NAME_TEMPLATE, portNo);

        Port port = new Port();
        port.setMonitorUnitId(monitorUnitId);
        port.setPortName(portName);
        port.setPortNo(portNo);
        port.setDescription("TCS create");
        port.setLinkSamplerUnitId(0);
        port.setPortType(34); // 虚拟端口
        port.setSetting("comm_host_dev.so");

        sitewebPersistentService.getConfigAPI().createForPort(port);
        return port;
    }

    /**
     * 创建采集单元
     */
    private SamplerUnit createSamplerUnit(DeviceConfigChangeDto deviceConfigChangeDto,
                                          Integer monitorUnitId, Port port) {
        SamplerUnit samplerUnit = new SamplerUnit();
        samplerUnit.setMonitorUnitId(monitorUnitId);
        samplerUnit.setPortId(port.getPortId());
        samplerUnit.setParentSamplerUnitId(0);
        samplerUnit.setSamplerType(Short.valueOf("18"));
        samplerUnit.setAddress(1);
        samplerUnit.setSpUnitInterval(2.0);
        samplerUnit.setDllPath("IDUHOST.so");
        samplerUnit.setConnectState(0);
        samplerUnit.setDescription("TCS create");
        samplerUnit.setSamplerUnitName(deviceConfigChangeDto.getSouthDeviceName());

        Integer samplerId = siteWebDefaultProvider.getDefaultSamplerIdByProtocolCode();
        samplerUnit.setSamplerId(samplerId);

        return sitewebPersistentService.getConfigAPI().createForSamplerUnit(samplerUnit);
    }

    /**
     * 处理设备类型
     */
    private Integer handleDeviceType(DeviceConfigChangeDto deviceConfigChangeDto) {
        JsonNode metadata = deviceConfigChangeDto.getMetadata();
//        String deviceType = metadata.get("deviceType").asText().replaceFirst("^0+(?!$)", "");
        String deviceType = "17";
//        String deviceSubType = metadata.get("deviceSubType").asText().replaceFirst("^0+(?!$)", "");
        String deviceSubType = "2";

//        String deviceSubTypeName = metadata.get("deviceSubTypeName").asText();
        String deviceSubTypeName = "机房环境";

        CmccDeviceTypeMap cmccDeviceTypeMap = cmccDeviceTypeMapService.getByDeviceTypeAndSubType(deviceType, deviceSubType);
        if (ObjectUtil.isNotEmpty(cmccDeviceTypeMap)) {
            return cmccDeviceTypeMap.getEquipmentCategoryId();
        }

        cmccDeviceTypeService.deleteByDeviceTypeAndDeviceSubType(deviceType, deviceSubType);
        Integer sitewebEquipmentCategoryId = cmccStandardMappingService.createSitewebEquipmentCategory(deviceSubTypeName);
        cmccDeviceTypeMapService.saveMapping(deviceType, deviceSubType, sitewebEquipmentCategoryId);

        return sitewebEquipmentCategoryId;
    }

    /**
     * 创建设备
     */
    private Equipment createEquipment(DeviceConfigChangeDto deviceConfigChangeDto,
                                      CmccSiteMap cmccSiteMap,
                                      CmccRoomMap cmccRoomMap,
                                      Integer monitorUnitId,
                                      Port port,
                                      SamplerUnit samplerUnit,
                                      Integer equipmentCategoryId) {
        CreateEquipmentDto createEquipmentDto = cmccConfigParser.parseDeviceInfo(deviceConfigChangeDto);
        createEquipmentDto.setStationId(cmccSiteMap.getStationId());
        createEquipmentDto.setHouseId(cmccRoomMap.getHouseId());
        createEquipmentDto.setInstantiated(true);
        createEquipmentDto.setEquipmentName(deviceConfigChangeDto.getSouthDeviceName());
        createEquipmentDto.setMonitorUnitId(monitorUnitId);
        createEquipmentDto.setSamplerUnitId(samplerUnit.getSamplerUnitId());
        int equipmentTemplateId = siteWebDefaultProvider.getDefaultEquipmentTemplateId(equipmentCategoryId,deviceConfigChangeDto.getSouthDeviceName());
        ResourceStructure resourceStructure = sitewebPersistentService.getConfigAPI().findByOriginIdAndStructureType(cmccSiteMap.getStationId(), 104);
        createEquipmentDto.setResourceStructureId(resourceStructure.getResourceStructureId());
        createEquipmentDto.setEquipmentTemplateId(equipmentTemplateId);
        return sitewebPersistentService.getConfigAPI().createForEquipment(createEquipmentDto);
    }

    /**
     * 保存设备映射
     */
    private void saveDeviceMap(Long gatewayId,
                               DeviceConfigChangeDto deviceConfigChangeDto,
                               Integer monitorUnitId,
                               Equipment equipment) {
        DeviceMap deviceMap = new DeviceMap();
        deviceMap.setDeviceId(deviceConfigChangeDto.getId());
        deviceMap.setGatewayId(gatewayId);
        deviceMap.setNorthMonitorUnitId(monitorUnitId);
        deviceMap.setNorthEquipmentTemplateId(equipment.getEquipmentTemplateId());
        deviceMap.setNorthEquipmentId(equipment.getEquipmentId());

        boolean save = deviceMapService.save(deviceMap);
    }


    private boolean saveCmccDeviceExt(DeviceConfigChangeDto deviceConfigChangeDto, Equipment equipment) {
        CmccDeviceExt cmccDeviceExt = new CmccDeviceExt();
        cmccDeviceExt.setDeviceGuid(deviceConfigChangeDto.getId());
        CmccDeviceType cmccDeviceType = cmccConfigParser.parseDeviceType(deviceConfigChangeDto);
        cmccDeviceExt.setDeviceTypeId(cmccDeviceType.getDeviceTypeId());
        cmccDeviceExt.setDeviceSubTypeId(cmccDeviceType.getDeviceSubTypeId());
        cmccDeviceExt.setEquipmentCategoryId(equipment.getEquipmentCategory());
        boolean save = cmccDeviceExtService.save(cmccDeviceExt);
        return save;
    }

    /**
     * 配置同步 - 根据网关ID列表同步设备
     * 比较hub数据库和北向deviceMap表，对设备进行增删操作
     * 
     * @param gatewayIdList 网关ID列表
     */
    public void handleDeviceSyncByGatewayIds(List<Long> gatewayIdList, List<GatewayMap> gatewayMapList) {
        if (CollUtil.isEmpty(gatewayIdList)) {
            return;
        }
        Map<Long, GatewayMap> gatewayMapMap = gatewayMapList.stream()
                .collect(Collectors.toMap(GatewayMap::getGatewayId, Function.identity()));
        for (Long gatewayId : gatewayIdList) {
            // 查询hub中该网关下的所有设备
            GatewayMap gatewayMap = gatewayMapMap.get(gatewayId);
            List<TcsDevice> hubDevices = tcsDeviceService.listByGatewayId(gatewayId);
            Map<Long,TcsDevice> tcsDeviceMap = hubDevices.stream()
                    .collect(Collectors.toMap(TcsDevice::getId, Function.identity()));

            Set<Long> hubDeviceIds = tcsDeviceMap.keySet();

            // 查询北向deviceMap表中该网关下的所有设备
            List<DeviceMap> northDeviceMaps = deviceMapService.getByGatewayId(gatewayId);
            Set<Long> northDeviceIds = northDeviceMaps.stream()
                    .map(DeviceMap::getDeviceId)
                    .collect(Collectors.toSet());

            // 找出hub有但北向没有的设备(需要新建)
            Set<Long> devicesToCreate = new HashSet<>(hubDeviceIds);
            devicesToCreate.removeAll(northDeviceIds);

            // 找出hub没有但北向有的设备(需要删除)
            Set<Long> devicesToDelete = new HashSet<>(northDeviceIds);
            devicesToDelete.removeAll(hubDeviceIds);

            // 找出两边都存在的设备(需要同步子组件)
            Set<Long> devicesToSync = new HashSet<>(hubDeviceIds);
            devicesToSync.retainAll(northDeviceIds);

            // TODO: 创建新设备 - 这里暂时无法实现，因为createDevice需要GatewayConfigChangeDto和其他复杂参数
            // 如果需要实现，需要重构createDevice方法或者从hub查询完整的设备信息
            for (Long deviceId : devicesToCreate){
                Integer northMonitorUnitId = gatewayMap.getNorthMonitorUnitId();
                DeviceConfigChangeDto deviceConfigChangeDto = tcsDeviceService.getDeviceConfigChangeDtoDeviceId(deviceId);
                deviceConfigChangeDto.setLifeCycleEvent(LifeCycleEventEnum.CREATE);
                deviceConfigChangeDto.getSignals().forEach(e -> e.setLifeCycleEvent(LifeCycleEventEnum.CREATE));
                deviceConfigChangeDto.getAlarms().forEach(e -> e.setLifeCycleEvent(LifeCycleEventEnum.CREATE));
                deviceConfigChangeDto.getControls().forEach(e -> e.setLifeCycleEvent(LifeCycleEventEnum.CREATE));
                createDevice(gatewayId, deviceConfigChangeDto, MapUtil.newHashMap(), MapUtil.newHashMap(), northMonitorUnitId);
            }
            
            // 删除不存在的设备
            for (Long deviceId : devicesToDelete) {
                DeviceConfigChangeDto deleteDto = new DeviceConfigChangeDto();
                deleteDto.setId(deviceId);
                handleDeviceDelete(deleteDto);
            }

            // 同步两边都存在的设备的子组件
            for (Long deviceId : devicesToSync) {
                TcsDevice hubDevice = hubDevices.stream()
                        .filter(d -> d.getId().equals(deviceId))
                        .findFirst()
                        .orElse(null);
                
                DeviceMap deviceMap = northDeviceMaps.stream()
                        .filter(d -> d.getDeviceId().equals(deviceId))
                        .findFirst()
                        .orElse(null);

                if (hubDevice != null && deviceMap != null) {
                    // 同步信号、告警、控制
                    cmccSignalHandler.handleSignalSyncByDeviceId(deviceId, deviceMap);
                    cmccAlarmHandler.handleAlarmSyncByDeviceId(deviceId, deviceMap);
                    cmccControlHandler.handleControlSyncByDeviceId(deviceId, deviceMap);
                }
            }
        }
    }

    /**
     * 配置同步 - 根据设备配置同步DTO列表同步设备
     * 传入的设备列表是北向需要同步的设备，需要与hub中的设备进行对比
     * 
     * @param deviceConfigSyncDTOList 设备配置同步DTO列表（北向需要同步的设备）
     */
    public void handleDeviceSyncByDeviceList(List<DeviceConfigSyncDTO> deviceConfigSyncDTOList) {
        if (CollUtil.isEmpty(deviceConfigSyncDTOList)) {
            return;
        }

        for (DeviceConfigSyncDTO syncDTO : deviceConfigSyncDTOList) {
            Long gatewayId = syncDTO.getGatewayId();
            List<Long> northDeviceIdList = syncDTO.getDeviceId();
            
            if (CollUtil.isEmpty(northDeviceIdList)) {
                continue;
            }

            // 北向需要同步的设备ID集合
            Set<Long> northDeviceIds = new HashSet<>(northDeviceIdList);

            // 查询hub中该网关下的所有设备
            List<TcsDevice> hubDevices = tcsDeviceService.listByDeviceIdList(northDeviceIdList);
            Set<Long> hubDeviceIds = hubDevices.stream()
                    .map(TcsDevice::getId)
                    .collect(Collectors.toSet());

            // 查询北向deviceMap表中的设备映射
            List<DeviceMap> northDeviceMaps = deviceMapService.listByDeviceIdList(northDeviceIdList);

            // 找出北向有但hub没有的设备(需要删除)
            Set<Long> devicesToDelete = new HashSet<>(northDeviceIds);
            devicesToDelete.removeAll(hubDeviceIds);

            // 找出两边都存在的设备(需要更新子组件)
            Set<Long> devicesToSync = new HashSet<>(northDeviceIds);
            devicesToSync.retainAll(hubDeviceIds);

            // 删除北向有但hub没有的设备
            for (Long deviceId : devicesToDelete) {
                DeviceConfigChangeDto deleteDto = new DeviceConfigChangeDto();
                deleteDto.setId(deviceId);
                handleDeviceDelete(deleteDto);
                log.info("设备同步: 删除北向设备 deviceId={}, gatewayId={}", deviceId, gatewayId);
            }

            // 更新两边都存在的设备的子组件（信号、告警、控制）
            for (Long deviceId : devicesToSync) {
                DeviceMap deviceMap = northDeviceMaps.stream()
                        .filter(d -> d.getDeviceId().equals(deviceId))
                        .findFirst()
                        .orElse(null);

                if (deviceMap != null) {
                    // 同步信号、告警、控制
                    cmccSignalHandler.handleSignalSyncByDeviceId(deviceId, deviceMap);
                    cmccAlarmHandler.handleAlarmSyncByDeviceId(deviceId, deviceMap);
                    cmccControlHandler.handleControlSyncByDeviceId(deviceId, deviceMap);
                    log.debug("设备同步: 同步设备子组件 deviceId={}, gatewayId={}", deviceId, gatewayId);
                }
            }
        }
    }
}
