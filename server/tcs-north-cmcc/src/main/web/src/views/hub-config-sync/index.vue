<template>
  <div class="hub-config-sync">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="font-bold text-lg">S6北向配置同步 - 网关映射</span>
          <div class="flex items-center gap-3">
            <el-button type="primary" @click="handleGatewayConfigSync">
              网关配置同步
            </el-button>
            <el-button type="primary" @click="handleConsistencyCheck">
              网关一致性检查
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选框 -->
      <div class="filter-container">
        <el-form :inline="true" :model="filterForm">
          <el-form-item label="插件ID">
            <el-input
              v-model="filterForm.pluginId"
              placeholder="请输入插件ID"
              clearable
              @clear="handleFilter"
            />
          </el-form-item>
          <el-form-item label="网关ID">
            <el-input
              v-model="filterForm.gatewayId"
              placeholder="请输入网关ID"
              clearable
              @clear="handleFilter"
            />
          </el-form-item>
          <el-form-item label="北向监控单元ID">
            <el-input
              v-model="filterForm.northMonitorUnitId"
              placeholder="请输入北向监控单元ID"
              clearable
              @clear="handleFilter"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        v-loading="loading"
        :data="filteredData"
        stripe
        border
        style="width: 100%"
        :row-class-name="getRowClassName"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="pluginId" label="插件ID" min-width="120" />
        <el-table-column prop="gatewayId" label="网关ID" min-width="120" />
        <el-table-column prop="northMonitorUnitId" label="北向监控单元ID" min-width="140" />
        <el-table-column prop="northStationId" label="局站ID" min-width="100" />
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 网关一致性检查弹窗 -->
    <GatewayConsistencyDialog
      v-model:visible="consistencyDialogVisible"
      @refresh="loadGatewayMappings"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getGatewayMappings,
  syncConfig,
  type GatewayMapping
} from "@/api/hub-config-sync";
import GatewayConsistencyDialog from "./components/GatewayConsistencyDialog.vue";

const router = useRouter();
const loading = ref(false);
const gatewayMappings = ref<GatewayMapping[]>([]);
const selectedGateways = ref<GatewayMapping[]>([]);
const highlightGatewayId = ref<number | null>(null);
const consistencyDialogVisible = ref(false);

// 筛选表单
const filterForm = ref({
  pluginId: "",
  gatewayId: "",
  northMonitorUnitId: ""
});

onMounted(() => {
  // 检查是否有需要高亮的网关ID
  const returnGatewayId = sessionStorage.getItem('highlightGatewayId');
  if (returnGatewayId) {
    highlightGatewayId.value = parseInt(returnGatewayId);
    sessionStorage.removeItem('highlightGatewayId');
  }
  
  loadGatewayMappings();
});

/**
 * 加载网关映射列表
 */
const loadGatewayMappings = async () => {
  try {
    loading.value = true;
    const response = await getGatewayMappings();
    if (response.state) {
      gatewayMappings.value = response.data || [];
    } else {
      ElMessage.error(response.err_msg || "加载网关映射列表失败");
    }
  } catch (error) {
    console.error("加载网关映射列表失败:", error);
    ElMessage.error("加载网关映射列表失败");
  } finally {
    loading.value = false;
  }
};

/**
 * 筛选后的数据
 */
const filteredData = computed(() => {
  let data = gatewayMappings.value;
  
  if (filterForm.value.pluginId) {
    data = data.filter(item => 
      item.pluginId?.toLowerCase().includes(filterForm.value.pluginId.toLowerCase())
    );
  }
  
  if (filterForm.value.gatewayId) {
    data = data.filter(item => 
      String(item.gatewayId).includes(filterForm.value.gatewayId)
    );
  }
  
  if (filterForm.value.northMonitorUnitId) {
    data = data.filter(item => 
      String(item.northMonitorUnitId).includes(filterForm.value.northMonitorUnitId)
    );
  }
  
  return data;
});

/**
 * 查询
 */
const handleFilter = () => {
  // 筛选逻辑已在computed中实现
};

/**
 * 重置
 */
const handleReset = () => {
  filterForm.value = {
    pluginId: "",
    gatewayId: "",
    northMonitorUnitId: ""
  };
};

/**
 * 查看详情
 */
const handleViewDetail = (row: GatewayMapping) => {
  // 保存当前网关ID用于返回时高亮
  sessionStorage.setItem('highlightGatewayId', String(row.gatewayId));
  
  router.push({
    name: "HubConfigSyncDevice",
    params: {
      gatewayId: row.gatewayId
    },
    query: {
      pluginId: row.pluginId,
      northMonitorUnitId: row.northMonitorUnitId
    }
  });
};

/**
 * 网关一致性检查
 */
const handleConsistencyCheck = () => {
  consistencyDialogVisible.value = true;
};

/**
 * 多选框选择变化
 */
const handleSelectionChange = (selection: GatewayMapping[]) => {
  selectedGateways.value = selection;
};

/**
 * 网关配置同步
 */
const handleGatewayConfigSync = async () => {
  if (selectedGateways.value.length === 0) {
    ElMessage.warning("请至少选择一个网关进行同步");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要同步选中的 ${selectedGateways.value.length} 个网关的配置吗？`,
      "确认同步",
      {
        type: "warning"
      }
    );

    loading.value = true;
    const gatewayIdList = selectedGateways.value.map(g => g.gatewayId);
    const response = await syncConfig({ gatewayIdList });
    
    if (response.state) {
      ElMessage.success("网关配置同步成功");
      await loadGatewayMappings();
    } else {
      ElMessage.error(response.err_msg || "网关配置同步失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("网关配置同步失败:", error);
      ElMessage.error("网关配置同步失败");
    }
  } finally {
    loading.value = false;
  }
};

/**
 * 获取行的className用于高亮
 */
const getRowClassName = ({ row }: { row: GatewayMapping }) => {
  if (highlightGatewayId.value && row.gatewayId === highlightGatewayId.value) {
    return 'highlight-row';
  }
  return '';
};
</script>

<style scoped>
.hub-config-sync {
  padding: 20px;
}

.box-card {
  min-height: calc(100vh - 120px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-container {
  margin-bottom: 20px;
}

:deep(.highlight-row) {
  background-color: #ecf5ff !important;
  animation: highlight-fade 2s ease-in-out;
}

@keyframes highlight-fade {
  0% {
    background-color: #a0cfff;
  }
  100% {
    background-color: #ecf5ff;
  }
}
</style>
