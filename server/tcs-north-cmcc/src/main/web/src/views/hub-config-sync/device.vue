<template>
  <div class="hub-config-sync-device">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div>
            <el-button
              type="default"
              size="small"
              @click="handleBack"
              style="margin-right: 10px"
            >
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <span class="font-bold text-lg">设备映射列表</span>
            <span class="text-sm text-gray-500" style="margin-left: 10px">
              网关ID: {{ gatewayId }}
            </span>
          </div>
          <el-button type="primary" @click="handleDeviceConfigSync">
            设备配置同步
          </el-button>
        </div>
      </template>

      <!-- 筛选框 -->
      <div class="filter-container">
        <el-form :inline="true" :model="filterForm">
          <el-form-item label="设备ID">
            <el-input
              v-model="filterForm.deviceId"
              placeholder="请输入设备ID"
              clearable
              @clear="handleFilter"
            />
          </el-form-item>
          <el-form-item label="北向设备ID">
            <el-input
              v-model="filterForm.northEquipmentId"
              placeholder="请输入北向设备ID"
              clearable
              @clear="handleFilter"
            />
          </el-form-item>
          <el-form-item label="北向监控单元ID">
            <el-input
              v-model="filterForm.northMonitorUnitId"
              placeholder="请输入北向监控单元ID"
              clearable
              @clear="handleFilter"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        v-loading="loading"
        :data="filteredData"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="gatewayId" label="网关ID" min-width="100" />
        <el-table-column prop="deviceId" label="设备ID" min-width="120" />
        <el-table-column prop="northEquipmentId" label="北向设备ID" min-width="120" />
        <el-table-column prop="northMonitorUnitId" label="北向监控单元ID" min-width="140" />
        <el-table-column prop="northEquipmentTemplateId" label="设备模板ID" min-width="120" />
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 设备详情弹窗（信号/告警/控制） -->
    <DeviceDetailDialog
      v-model:visible="detailDialogVisible"
      :device-mapping="selectedDevice"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowLeft } from "@element-plus/icons-vue";
import {
  getDeviceMappings,
  syncConfig,
  type DeviceMapping,
  type DeviceConfigSyncDTO
} from "@/api/hub-config-sync";
import DeviceDetailDialog from "./components/DeviceDetailDialog.vue";

const router = useRouter();
const route = useRoute();
const loading = ref(false);
const deviceMappings = ref<DeviceMapping[]>([]);
const selectedDevices = ref<DeviceMapping[]>([]);
const detailDialogVisible = ref(false);
const selectedDevice = ref<DeviceMapping | null>(null);

const gatewayId = computed(() => parseInt(route.params.gatewayId as string));

// 筛选表单
const filterForm = ref({
  deviceId: "",
  northEquipmentId: "",
  northMonitorUnitId: ""
});

onMounted(() => {
  loadDeviceMappings();
});

/**
 * 加载设备映射列表
 */
const loadDeviceMappings = async () => {
  try {
    loading.value = true;
    const response = await getDeviceMappings(gatewayId.value);
    if (response.state) {
      deviceMappings.value = response.data || [];
    } else {
      ElMessage.error(response.err_msg || "加载设备映射列表失败");
    }
  } catch (error) {
    console.error("加载设备映射列表失败:", error);
    ElMessage.error("加载设备映射列表失败");
  } finally {
    loading.value = false;
  }
};

/**
 * 筛选后的数据
 */
const filteredData = computed(() => {
  let data = deviceMappings.value;
  
  if (filterForm.value.deviceId) {
    data = data.filter(item => 
      String(item.deviceId).includes(filterForm.value.deviceId)
    );
  }
  
  if (filterForm.value.northEquipmentId) {
    data = data.filter(item => 
      String(item.northEquipmentId).includes(filterForm.value.northEquipmentId)
    );
  }
  
  if (filterForm.value.northMonitorUnitId) {
    data = data.filter(item => 
      String(item.northMonitorUnitId).includes(filterForm.value.northMonitorUnitId)
    );
  }
  
  return data;
});

/**
 * 查询
 */
const handleFilter = () => {
  // 筛选逻辑已在computed中实现
};

/**
 * 重置
 */
const handleReset = () => {
  filterForm.value = {
    deviceId: "",
    northEquipmentId: "",
    northMonitorUnitId: ""
  };
};

/**
 * 查看详情
 */
const handleViewDetail = (row: DeviceMapping) => {
  selectedDevice.value = row;
  detailDialogVisible.value = true;
};

/**
 * 多选框选择变化
 */
const handleSelectionChange = (selection: DeviceMapping[]) => {
  selectedDevices.value = selection;
};

/**
 * 设备配置同步
 */
const handleDeviceConfigSync = async () => {
  if (selectedDevices.value.length === 0) {
    ElMessage.warning("请至少选择一个设备进行同步");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要同步选中的 ${selectedDevices.value.length} 个设备的配置吗？`,
      "确认同步",
      {
        type: "warning"
      }
    );

    loading.value = true;
    
    // 构造设备配置同步DTO列表
    const deviceConfigSyncDTOList: DeviceConfigSyncDTO[] = [{
      gatewayId: gatewayId.value,
      deviceId: selectedDevices.value.map(d => d.deviceId)
    }];
    
    const response = await syncConfig({ deviceConfigSyncDTOList });
    
    if (response.state) {
      ElMessage.success("设备配置同步成功");
      await loadDeviceMappings();
    } else {
      ElMessage.error(response.err_msg || "设备配置同步失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("设备配置同步失败:", error);
      ElMessage.error("设备配置同步失败");
    }
  } finally {
    loading.value = false;
  }
};

/**
 * 返回网关列表
 */
const handleBack = () => {
  router.push({ name: "HubConfigSyncGateway" });
};
</script>

<style scoped>
.hub-config-sync-device {
  padding: 20px;
}

.box-card {
  min-height: calc(100vh - 120px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-container {
  margin-bottom: 20px;
}
</style>
