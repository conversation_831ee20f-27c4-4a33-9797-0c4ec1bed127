<template>
  <el-dialog
    v-model="dialogVisible"
    title="设备详情"
    width="90%"
    :before-close="handleClose"
  >
    <div v-if="props.deviceMapping">
      <div class="device-info">
        <div>
          <span>设备ID: {{ props.deviceMapping.deviceId }}</span>
          <span style="margin-left: 20px">
            北向设备ID: {{ props.deviceMapping.northEquipmentId }}
          </span>
        </div>
      </div>

      <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
        <!-- 信号Tab -->
        <el-tab-pane label="信号" name="signal">
          <SignalTab
            ref="signalTabRef"
            :device-mapping="props.deviceMapping"
          />
        </el-tab-pane>

        <!-- 告警Tab -->
        <el-tab-pane label="告警" name="alarm">
          <AlarmTab
            ref="alarmTabRef"
            :device-mapping="props.deviceMapping"
          />
        </el-tab-pane>

        <!-- 控制Tab -->
        <el-tab-pane label="控制" name="control">
          <ControlTab
            ref="controlTabRef"
            :device-mapping="props.deviceMapping"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import type { DeviceMapping } from "@/api/hub-config-sync";
import SignalTab from "./SignalTab.vue";
import AlarmTab from "./AlarmTab.vue";
import ControlTab from "./ControlTab.vue";

interface Props {
  visible: boolean;
  deviceMapping: DeviceMapping | null;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const dialogVisible = ref(false);
const activeTab = ref("signal");
const signalTabRef = ref();
const alarmTabRef = ref();
const controlTabRef = ref();

watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      activeTab.value = "signal";
      // 延迟加载确保组件已挂载，加载所有Tab的数据
      setTimeout(() => {
        loadAllTabsData();
      }, 100);
    }
  }
);

watch(dialogVisible, (val) => {
  emit("update:visible", val);
});

/**
 * 加载所有Tab的数据
 */
const loadAllTabsData = () => {
  if (signalTabRef.value) {
    signalTabRef.value.loadData();
  }
  if (alarmTabRef.value) {
    alarmTabRef.value.loadData();
  }
  if (controlTabRef.value) {
    controlTabRef.value.loadData();
  }
};

/**
 * Tab切换（保留此方法以防需要重新加载）
 */
const handleTabClick = (tab: any) => {
  // Tab切换时不需要重新加载，因为已经在打开弹窗时加载了所有数据
  // 如果需要每次切换都重新加载，可以取消注释下面的代码
  /*
  if (tab.name === "signal" && signalTabRef.value) {
    signalTabRef.value.loadData();
  } else if (tab.name === "alarm" && alarmTabRef.value) {
    alarmTabRef.value.loadData();
  } else if (tab.name === "control" && controlTabRef.value) {
    controlTabRef.value.loadData();
  }
  */
};

/**
 * 关闭弹窗
 */
const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.device-info {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
