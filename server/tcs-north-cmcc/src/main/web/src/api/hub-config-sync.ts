import { http } from "@/utils/http";

export type ApiResponse<T> = {
  state: boolean;
  timestamp: number;
  data: T;
  err_msg: string | null;
  err_code: string | null;
};

/**
 * 网关映射接口（对应 GatewayMap 实体）
 */
export interface GatewayMapping {
  pluginId: string; // 插件ID
  gatewayId: number; // Hub全局网关ID
  northMonitorUnitId: number; // Siteweb采集单元ID
  northStationId?: number; // 局站ID
  deleted?: boolean; // 逻辑删除标志
}

/**
 * 设备映射接口（对应 DeviceMap 实体）
 */
export interface DeviceMapping {
  gatewayId: number; // 网关ID
  northMonitorUnitId: number; // 北向监控单元ID
  deviceId: number; // 设备ID
  northEquipmentId: number; // 北向设备ID
  northEquipmentTemplateId?: number; // 设备模板ID
  deleted?: boolean; // 逻辑删除标志
}

/**
 * 信号映射接口（对应 SignalMap 实体）
 */
export interface SignalMapping {
  deviceId: number; // 设备ID
  northEquipmentId: number; // 北向设备ID
  signalId: number; // 信号ID
  northSignalId: number; // 北向信号ID
  deleted?: boolean; // 逻辑删除标志
}

/**
 * 告警映射接口（对应 AlarmMap 实体）
 */
export interface AlarmMapping {
  deviceId: number; // 设备ID
  northEquipmentId: number; // 北向设备ID
  alarmId: number; // 告警ID
  northEventId: number; // 北向事件ID
  northEventConditionId: number; // 北向事件条件ID
  deleted?: boolean; // 逻辑删除标志
}

/**
 * 控制映射接口（对应 ControlMap 实体）
 */
export interface ControlMapping {
  deviceId: number; // 设备ID
  northEquipmentId: number; // 北向设备ID
  controlId: number; // 控制ID
  northControlId: number; // 北向控制ID
  deleted?: boolean; // 逻辑删除标志
}

/**
 * 设备配置同步DTO
 */
export interface DeviceConfigSyncDTO {
  gatewayId: number; // 网关ID
  deviceId: number[]; // 设备ID列表
}

/**
 * 信号配置同步DTO
 */
export interface SignalConfigSyncDTO {
  gatewayId: number; // 网关ID
  deviceId: number; // 设备ID
  signalIdList: number[]; // 信号ID列表
}

/**
 * 告警配置同步DTO
 */
export interface AlarmConfigSyncDTO {
  gatewayId: number; // 网关ID
  deviceId: number; // 设备ID
  alarmIdList: number[]; // 告警ID列表
}

/**
 * 控制配置同步DTO
 */
export interface ControlConfigSyncDTO {
  gatewayId: number; // 网关ID
  deviceId: number; // 设备ID
  controlIdList: number[]; // 控制ID列表
}

/**
 * 配置同步DTO
 */
export interface ConfigSyncDto {
  gatewayIdList?: number[]; // 网关ID列表
  deviceConfigSyncDTOList?: DeviceConfigSyncDTO[]; // 设备配置同步列表
  signalConfigSyncDTOList?: SignalConfigSyncDTO[]; // 信号配置同步列表
  alarmConfigSyncDTOList?: AlarmConfigSyncDTO[]; // 告警配置同步列表
  controlConfigSyncDTOList?: ControlConfigSyncDTO[]; // 控制配置同步列表
}

// ==================== API接口 ====================

/**
 * 获取所有网关映射列表
 */
export const getGatewayMappings = () => {
  return http.request<ApiResponse<GatewayMapping[]>>(
    "get",
    "/api/thing/tcs-north-cmcc/gateway-map/list"
  );
};

/**
 * 获取设备映射列表（根据网关ID）
 */
export const getDeviceMappings = (gatewayId: number) => {
  return http.request<ApiResponse<DeviceMapping[]>>(
    "get",
    `/api/thing/tcs-north-cmcc/device-map/gateway/${gatewayId}`
  );
};

/**
 * 获取信号映射列表（根据设备ID）
 */
export const getSignalMappings = (deviceId: number) => {
  return http.request<ApiResponse<SignalMapping[]>>(
    "get",
    `/api/thing/tcs-north-cmcc/signal-map/device/${deviceId}`
  );
};

/**
 * 获取告警映射列表（根据设备ID）
 */
export const getAlarmMappings = (deviceId: number) => {
  return http.request<ApiResponse<AlarmMapping[]>>(
    "get",
    `/api/thing/tcs-north-cmcc/alarm-map/device/${deviceId}`
  );
};

/**
 * 获取控制映射列表（根据设备ID）
 */
export const getControlMappings = (deviceId: number) => {
  return http.request<ApiResponse<ControlMapping[]>>(
    "get",
    `/api/thing/tcs-north-cmcc/control-map/device/${deviceId}`
  );
};

/**
 * 创建网关映射
 */
export const createGatewayMapping = (gatewayMap: GatewayMapping) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-cmcc/gateway-map/create",
    { data: gatewayMap }
  );
};

/**
 * 创建设备映射
 */
export const createDeviceMapping = (deviceMap: DeviceMapping) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-cmcc/device-map/create",
    { data: deviceMap }
  );
};

/**
 * 创建信号映射
 */
export const createSignalMapping = (signalMap: SignalMapping) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-cmcc/signal-map/create",
    { data: signalMap }
  );
};

/**
 * 创建告警映射
 */
export const createAlarmMapping = (alarmMap: AlarmMapping) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-cmcc/alarm-map/create",
    { data: alarmMap }
  );
};

/**
 * 创建控制映射
 */
export const createControlMapping = (controlMap: ControlMapping) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-cmcc/control-map/create",
    { data: controlMap }
  );
};

/**
 * 删除信号映射
 */
export const deleteSignalMapping = (deviceId: number, signalId: number, northSignalId: number) => {
  return http.request<ApiResponse<string>>(
    "delete",
    "/api/thing/tcs-north-cmcc/signal-map/delete",
    {
      params: { deviceId, signalId, northSignalId }
    }
  );
};

/**
 * 删除告警映射
 */
export const deleteAlarmMapping = (
  deviceId: number,
  northEquipmentId: number,
  alarmId: number,
  northEventId: number,
  northEventConditionId: number
) => {
  return http.request<ApiResponse<string>>(
    "delete",
    "/api/thing/tcs-north-cmcc/alarm-map/delete",
    {
      params: { deviceId, northEquipmentId, alarmId, northEventId, northEventConditionId }
    }
  );
};

/**
 * 删除控制映射
 */
export const deleteControlMapping = (deviceId: number, controlId: number, northControlId: number) => {
  return http.request<ApiResponse<string>>(
    "delete",
    "/api/thing/tcs-north-cmcc/control-map/delete",
    {
      params: { deviceId, controlId, northControlId }
    }
  );
};

/**
 * 配置同步
 */
export const syncConfig = (configSyncDto: ConfigSyncDto) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-cmcc/gateway-map/sync-config",
    { data: configSyncDto }
  );
};
