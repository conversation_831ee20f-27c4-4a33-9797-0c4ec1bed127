package com.siteweb.tcs.hub.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.common.util.LocaleMessageSourceUtil;
import com.siteweb.tcs.hub.dal.dto.*;
import com.siteweb.tcs.hub.dal.entity.TcsAlarm;
import com.siteweb.tcs.hub.dal.entity.TcsControl;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.dal.entity.TcsSignal;
import com.siteweb.tcs.hub.dal.mapper.TcsDeviceMapper;
import com.siteweb.tcs.common.enums.LifeCycleEventEnum;
import com.siteweb.tcs.common.enums.ThingType;
import com.siteweb.tcs.hub.service.ITcsAlarmService;
import com.siteweb.tcs.hub.service.ITcsControlService;
import com.siteweb.tcs.hub.service.ITcsDeviceService;
import com.siteweb.tcs.hub.service.ITcsSignalService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备表服务实现类
 */
@Service
public class TcsDeviceServiceImpl extends ServiceImpl<TcsDeviceMapper, TcsDevice> implements ITcsDeviceService {

    @Resource
    private TcsDeviceMapper tcsDeviceMapper;

    @Autowired
    private ITcsSignalService signalService;

    @Autowired
    private ITcsAlarmService alarmService;

    @Autowired
    private ITcsControlService controlService;

    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public List<TcsDevice> listByGatewayId(Long gatewayId) {
        return tcsDeviceMapper.selectByGatewayId(gatewayId);
    }

    @Override
    public TcsDevice getBySouthDeviceId(String southDeviceId) {
        return tcsDeviceMapper.selectBySouthDeviceId(southDeviceId);
    }

    @Override
    public DeviceConfigChangeDto getDeviceConfigChangeDtoDeviceId(Long deviceId) {
        TcsDevice device = tcsDeviceMapper.selectByDeviceId(deviceId);
        DeviceConfigChangeDto deviceConfigChangeDto = new DeviceConfigChangeDto();
        BeanUtils.copyProperties(device, deviceConfigChangeDto);
        List<AlarmConfigChangeDto> alarmConfigChangeDtoList = alarmService.listDtoByDeviceId(deviceId);
        List<ControlConfigChangeDto> controlConfigChangeDtoList = controlService.listDtoByDeviceId(deviceId);
        List<SignalConfigChangeDto> signalConfigChangeDtoList = signalService.listDtoByDeviceId(deviceId);
        deviceConfigChangeDto.setAlarms(alarmConfigChangeDtoList);
        deviceConfigChangeDto.setSignals(signalConfigChangeDtoList);
        deviceConfigChangeDto.setControls(controlConfigChangeDtoList);
        return deviceConfigChangeDto;
    }

    @Override
    public List<TcsDevice> listByDeviceIdList(List<Long> deviceIdList) {
        LambdaQueryWrapper<TcsDevice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TcsDevice::getId,deviceIdList);
        return tcsDeviceMapper.selectList(queryWrapper);
    }

    @Override
    public TcsDevice getDeviceWithDetails(Long deviceId) {
        return tcsDeviceMapper.selectDeviceWithDetails(deviceId);
    }

    @Override
    public List<TcsDevice> listByDeviceType(Integer deviceType) {
        return tcsDeviceMapper.selectByDeviceType(deviceType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateDevice(TcsDevice device) {
        return saveOrUpdate(device);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConfigChangeResult handleDeviceConfigChange(DeviceConfigChangeDto configDto) {
        try {
            // 参数验证
            if (configDto == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("config.data.empty"),
                        ThingType.DEVICE);
            }

            if (configDto.getLifeCycleEvent() == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("config.lifecycle.empty"),
                        ThingType.DEVICE);
            }
            // 根据生命周期事件类型处理
            ConfigChangeResult result;
            switch (configDto.getLifeCycleEvent()) {
                case CREATE:
                    result = handleDeviceCreate(configDto);
                    break;
                case UPDATE:
                    result = handleDeviceUpdate(configDto);
                    break;
                case DELETE:
                    result = handleDeviceDelete(configDto);
                    break;
                case NOT_CHANGE:
                    result = handleDeviceNotChange(configDto);
                    break;
                default:
                    return ConfigChangeResult.failure(
                            messageSourceUtil.getMessage("config.lifecycle.unsupported", new Object[]{configDto.getLifeCycleEvent()}),
                            ThingType.DEVICE);
            }

            // 处理子配置变更（如果有且不是CREATE操作）
            // CREATE操作的子对象已经在handleDeviceCreate中处理过了
            if (result.isSuccess() && configDto.getLifeCycleEvent() != LifeCycleEventEnum.CREATE) {
                // 收集子配置变更的结果
                List<SignalConfigChangeDto> changedSignals = new ArrayList<>();
                List<AlarmConfigChangeDto> changedAlarms = new ArrayList<>();
                List<ControlConfigChangeDto> changedControls = new ArrayList<>();

                // 处理信号配置变更
                if (!CollectionUtils.isEmpty(configDto.getSignals())) {
                    for (SignalConfigChangeDto signalDto : configDto.getSignals()) {
                        signalDto.setDeviceId(configDto.getId());
                        ConfigChangeResult signalResult = signalService.handleSignalConfigChange(signalDto);
                        if (!signalResult.isSuccess()) {
                            throw new RuntimeException(messageSourceUtil.getMessage("device.signal.change.failed",
                                    new Object[]{signalResult.getErrorMessage()}));
                        }
                        if (signalResult.getConfigData() != null) {
                            changedSignals.add((SignalConfigChangeDto) signalResult.getConfigData());
                        }
                    }
                }

                // 处理告警配置变更
                if (!CollectionUtils.isEmpty(configDto.getAlarms())) {
                    for (AlarmConfigChangeDto alarmDto : configDto.getAlarms()) {
                        alarmDto.setDeviceId(configDto.getId());
                        ConfigChangeResult alarmResult = alarmService.handleAlarmConfigChange(alarmDto);
                        if (!alarmResult.isSuccess()) {
                            throw new RuntimeException(messageSourceUtil.getMessage("device.alarm.change.failed",
                                    new Object[]{alarmResult.getErrorMessage()}));
                        }
                        if (alarmResult.getConfigData() != null) {
                            changedAlarms.add((AlarmConfigChangeDto) alarmResult.getConfigData());
                        }
                    }
                }

                // 处理控制配置变更
                if (!CollectionUtils.isEmpty(configDto.getControls())) {
                    for (ControlConfigChangeDto controlDto : configDto.getControls()) {
                        controlDto.setDeviceId(configDto.getId());
                        ConfigChangeResult controlResult = controlService.handleControlConfigChange(controlDto);
                        if (!controlResult.isSuccess()) {
                            throw new RuntimeException(messageSourceUtil.getMessage("device.control.change.failed",
                                    new Object[]{controlResult.getErrorMessage()}));
                        }
                        if (controlResult.getConfigData() != null) {
                            changedControls.add((ControlConfigChangeDto) controlResult.getConfigData());
                        }
                    }
                }

                // 将子配置变更结果设置到返回的DeviceConfigChangeDto中
                if (result.getConfigData() != null) {
                    DeviceConfigChangeDto deviceResultDto = (DeviceConfigChangeDto) result.getConfigData();
                    deviceResultDto.setSignals(changedSignals.isEmpty() ? null : changedSignals);
                    deviceResultDto.setAlarms(changedAlarms.isEmpty() ? null : changedAlarms);
                    deviceResultDto.setControls(changedControls.isEmpty() ? null : changedControls);
                }
            }

            return result;
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("config.change.failed"),
                    e.getMessage(), ThingType.DEVICE);
        }
    }

    private ConfigChangeResult handleDeviceNotChange(DeviceConfigChangeDto configDto) {
        DeviceConfigChangeDto result = new DeviceConfigChangeDto();
        result.setId(configDto.getId());
        result.setLifeCycleEvent(LifeCycleEventEnum.NOT_CHANGE);
        ConfigChangeResult configChangeResult = new ConfigChangeResult();
        configChangeResult.setSuccess(true);
        configChangeResult.setConfigData(result);
        configChangeResult.setEntityType(ThingType.DEVICE);
        configChangeResult.setEntityId(configDto.getSouthDeviceId());
        return configChangeResult;
    }

    /**
     * 处理设备创建
     */
    private ConfigChangeResult handleDeviceCreate(DeviceConfigChangeDto configDto) {
        try {
            // 检查南向设备ID是否已存在
            if (configDto.getSouthDeviceId() != null) {
                TcsDevice existingDevice = getBySouthDeviceId(configDto.getSouthDeviceId());
                if (existingDevice != null) {
                    return ConfigChangeResult.failure(
                            messageSourceUtil.getMessage("device.south.id.exists", new Object[]{configDto.getSouthDeviceId()}),
                            ThingType.DEVICE);
                }
            }

            // 创建设备实体
            TcsDevice device = new TcsDevice();
            BeanUtils.copyProperties(configDto, device);
            device.setDeleted(false);

            // 保存设备
            boolean success = save(device);
            if (success) {
                // 创建返回的ConfigChangeDto，包含southId和id
                DeviceConfigChangeDto resultDto = new DeviceConfigChangeDto();
                BeanUtils.copyProperties(device, resultDto);
                resultDto.setLifeCycleEvent(configDto.getLifeCycleEvent());

                // 处理子配置变更并收集CREATE的数据
                List<SignalConfigChangeDto> createdSignals = new ArrayList<>();
                List<AlarmConfigChangeDto> createdAlarms = new ArrayList<>();
                List<ControlConfigChangeDto> createdControls = new ArrayList<>();

                // 处理信号配置变更
                if (!CollectionUtils.isEmpty(configDto.getSignals())) {
                    for (SignalConfigChangeDto signalDto : configDto.getSignals()) {
                        if (signalDto.getLifeCycleEvent() == LifeCycleEventEnum.CREATE) {
                            signalDto.setDeviceId(device.getId());
                            ConfigChangeResult signalResult = signalService.handleSignalConfigChange(signalDto);
                            if (signalResult.isSuccess() && signalResult.getConfigData() != null) {
                                createdSignals.add((SignalConfigChangeDto) signalResult.getConfigData());
                            }
                        }
                    }
                }

                // 处理告警配置变更
                if (!CollectionUtils.isEmpty(configDto.getAlarms())) {
                    for (AlarmConfigChangeDto alarmDto : configDto.getAlarms()) {
                        if (alarmDto.getLifeCycleEvent() == LifeCycleEventEnum.CREATE) {
                            alarmDto.setDeviceId(device.getId());
                            ConfigChangeResult alarmResult = alarmService.handleAlarmConfigChange(alarmDto);
                            if (alarmResult.isSuccess() && alarmResult.getConfigData() != null) {
                                createdAlarms.add((AlarmConfigChangeDto) alarmResult.getConfigData());
                            }
                        }
                    }
                }

                // 处理控制配置变更
                if (!CollectionUtils.isEmpty(configDto.getControls())) {
                    for (ControlConfigChangeDto controlDto : configDto.getControls()) {
                        if (controlDto.getLifeCycleEvent() == LifeCycleEventEnum.CREATE) {
                            controlDto.setDeviceId(device.getId());
                            ConfigChangeResult controlResult = controlService.handleControlConfigChange(controlDto);
                            if (controlResult.isSuccess() && controlResult.getConfigData() != null) {
                                createdControls.add((ControlConfigChangeDto) controlResult.getConfigData());
                            }
                        }
                    }
                }

                // 设置子对象到结果DTO中
                resultDto.setSignals(createdSignals.isEmpty() ? null : createdSignals);
                resultDto.setAlarms(createdAlarms.isEmpty() ? null : createdAlarms);
                resultDto.setControls(createdControls.isEmpty() ? null : createdControls);

                return ConfigChangeResult.success(device.getSouthDeviceId(), ThingType.DEVICE, resultDto);
            } else {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("device.create.failed"),
                        ThingType.DEVICE);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("device.create.failed"),
                    e.getMessage(), ThingType.DEVICE);
        }
    }

    /**
     * 处理设备更新
     */
    private ConfigChangeResult handleDeviceUpdate(DeviceConfigChangeDto configDto) {
        try {
            if (configDto.getId() == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("device.update.id.required"),
                        ThingType.DEVICE);
            }

            // 检查设备是否存在
            TcsDevice existingDevice = getById(configDto.getId());
            if (existingDevice == null) {
                return ConfigChangeResult.failure(
                        messageSourceUtil.getMessage("device.not.exists", new Object[]{configDto.getId()}),
                        ThingType.DEVICE);
            }

            // 检查南向设备ID是否与其他设备冲突
            if (configDto.getSouthDeviceId() != null &&
                    !configDto.getSouthDeviceId().equals(existingDevice.getSouthDeviceId())) {
                TcsDevice conflictDevice = getBySouthDeviceId(configDto.getSouthDeviceId());
                if (conflictDevice != null && !conflictDevice.getId().equals(configDto.getId())) {
                    return ConfigChangeResult.failure(
                            messageSourceUtil.getMessage("device.south.id.conflict", new Object[]{configDto.getSouthDeviceId()}),
                            ThingType.DEVICE);
                }
            }

            // 更新设备实体
            BeanUtils.copyProperties(configDto, existingDevice, "id");

            // 保存更新
            boolean success = updateById(existingDevice);
            if (success) {
                // 创建返回的ConfigChangeDto，包含更新后的数据
                DeviceConfigChangeDto resultDto = new DeviceConfigChangeDto();
                BeanUtils.copyProperties(existingDevice, resultDto);
                resultDto.setLifeCycleEvent(LifeCycleEventEnum.UPDATE);

                return ConfigChangeResult.success(existingDevice.getSouthDeviceId(), ThingType.DEVICE, resultDto);
            } else {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("device.update.failed"),
                        ThingType.DEVICE);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("device.update.failed"),
                    e.getMessage(), ThingType.DEVICE);
        }
    }

    /**
     * 处理设备删除
     */
    private ConfigChangeResult handleDeviceDelete(DeviceConfigChangeDto configDto) {
        try {
            if (configDto.getId() == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("device.delete.id.required"),
                        ThingType.DEVICE);
            }

            // 检查设备是否存在
            TcsDevice existingDevice = tcsDeviceMapper.selectByDeviceId(configDto.getId());
            if (existingDevice == null) {
                return ConfigChangeResult.failure(
                        messageSourceUtil.getMessage("device.not.exists", new Object[]{configDto.getId()}),
                        ThingType.DEVICE);
            }

            // 级联删除该设备下的所有信号、控制、告警
            List<SignalConfigChangeDto> deletedSignals = new ArrayList<>();
            List<AlarmConfigChangeDto> deletedAlarms = new ArrayList<>();
            List<ControlConfigChangeDto> deletedControls = new ArrayList<>();

            // 删除信号
            List<TcsSignal> signalsUnderDevice = signalService.listByDeviceId(configDto.getId());
            for (TcsSignal signal : signalsUnderDevice) {
                SignalConfigChangeDto signalDeleteDto = new SignalConfigChangeDto();
                signalDeleteDto.setId(signal.getId());
                signalDeleteDto.setLifeCycleEvent(LifeCycleEventEnum.DELETE);

                ConfigChangeResult signalResult = signalService.handleSignalConfigChange(signalDeleteDto);
                if (!signalResult.isSuccess()) {
                    throw new RuntimeException(messageSourceUtil.getMessage("device.signal.delete.failed",
                            new Object[]{signal.getId(), signalResult.getErrorMessage()}));
                }
                if (signalResult.getConfigData() != null) {
                    deletedSignals.add((SignalConfigChangeDto) signalResult.getConfigData());
                }
            }

            // 删除告警
            List<TcsAlarm> alarmsUnderDevice = alarmService.listByDeviceId(configDto.getId());
            for (TcsAlarm alarm : alarmsUnderDevice) {
                AlarmConfigChangeDto alarmDeleteDto = new AlarmConfigChangeDto();
                alarmDeleteDto.setId(alarm.getId());
                alarmDeleteDto.setLifeCycleEvent(LifeCycleEventEnum.DELETE);

                ConfigChangeResult alarmResult = alarmService.handleAlarmConfigChange(alarmDeleteDto);
                if (!alarmResult.isSuccess()) {
                    throw new RuntimeException(messageSourceUtil.getMessage("device.alarm.delete.failed",
                            new Object[]{alarm.getId(), alarmResult.getErrorMessage()}));
                }
                if (alarmResult.getConfigData() != null) {
                    deletedAlarms.add((AlarmConfigChangeDto) alarmResult.getConfigData());
                }
            }

            // 删除控制
            List<TcsControl> controlsUnderDevice = controlService.listByDeviceId(configDto.getId());
            for (TcsControl control : controlsUnderDevice) {
                ControlConfigChangeDto controlDeleteDto = new ControlConfigChangeDto();
                controlDeleteDto.setId(control.getId());
                controlDeleteDto.setLifeCycleEvent(LifeCycleEventEnum.DELETE);

                ConfigChangeResult controlResult = controlService.handleControlConfigChange(controlDeleteDto);
                if (!controlResult.isSuccess()) {
                    throw new RuntimeException(messageSourceUtil.getMessage("device.control.delete.failed",
                            new Object[]{control.getId(), controlResult.getErrorMessage()}));
                }
                if (controlResult.getConfigData() != null) {
                    deletedControls.add((ControlConfigChangeDto) controlResult.getConfigData());
                }
            }

            // 逻辑删除设备
            boolean success = removeById(configDto.getId());
            if (success) {
                // 创建返回的ConfigChangeDto，包含要删除的设备ID和删除的子对象列表
                DeviceConfigChangeDto resultDto = new DeviceConfigChangeDto();
                BeanUtils.copyProperties(configDto, resultDto);
                resultDto.setId(existingDevice.getId());
                resultDto.setSouthDeviceId(existingDevice.getSouthDeviceId());
                resultDto.setLifeCycleEvent(LifeCycleEventEnum.DELETE);
//                resultDto.setSignals(deletedSignals.isEmpty() ? null : deletedSignals);
//                resultDto.setAlarms(deletedAlarms.isEmpty() ? null : deletedAlarms);
//                resultDto.setControls(deletedControls.isEmpty() ? null : deletedControls);

                return ConfigChangeResult.success(existingDevice.getSouthDeviceId(), ThingType.DEVICE, resultDto);
            } else {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("device.delete.failed"),
                        ThingType.DEVICE);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("device.delete.failed"),
                    e.getMessage(), ThingType.DEVICE);
        }
    }
}