package com.siteweb.tcs.hub.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.hub.dal.entity.TcsAlarm;
import com.siteweb.tcs.hub.dal.dto.AlarmConfigChangeDto;
import com.siteweb.tcs.hub.dal.dto.ConfigChangeResult;

import java.util.List;

/**
 * 告警表服务接口
 */
public interface ITcsAlarmService extends IService<TcsAlarm> {

    /**
     * 根据设备ID查询告警列表
     * @param deviceId 设备ID
     * @return 告警列表
     */
    List<TcsAlarm> listByDeviceId(Long deviceId);

    List<AlarmConfigChangeDto> listDtoByDeviceId(Long deviceId);

    /**
     * 根据南向告警ID查询告警
     * @param southAlarmId 南向告警ID
     * @return 告警信息
     */
    TcsAlarm getBySouthAlarmId(Long deviceId,String southAlarmId);

    /**
     * 根据关联信号ID查询告警列表
     * @param relatedSignalId 关联信号ID
     * @return 告警列表
     */
    List<TcsAlarm> listByRelatedSignalId(Long relatedSignalId);

    /**
     * 保存或更新告警信息
     * @param alarm 告警信息
     * @return 是否成功
     */
    boolean saveOrUpdateAlarm(TcsAlarm alarm);

    /**
     * 处理告警配置变更
     * @param configDto 告警配置变更DTO
     * @return 配置变更结果
     */
    ConfigChangeResult handleAlarmConfigChange(AlarmConfigChangeDto configDto);
}