package com.siteweb.tcs.backend.service;

import com.siteweb.tcs.common.runtime.ThingConnectPlugin;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.List;
import java.util.stream.Stream;

/**
 * 插件权限管理服务
 * 专门处理插件相关的权限、菜单、授权码等数据库操作
 * <AUTHOR> (2024-07-15)
 */
@Slf4j
@Service
public class PluginPermissionService {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Value("${spring.plugins.runtime-mode:production}")
    private String runtimeMode;

    @Value("${tcs.plugin.migration.enabled:true}")
    private boolean migrationEnabled;

    @Value("${tcs.plugin.migration.table-prefix:plugin_}")
    private String tablePrefix;

    @Value("${tcs.plugin.cleanup.enabled:true}")
    private boolean cleanupEnabled;

    /**
     * 判断是否为开发模式
     * @return true if development mode, false otherwise
     */
    private boolean isDevelopment() {
        return "development".equals(runtimeMode);
    }

    /**
     * 执行插件权限相关的数据库迁移
     * @param pluginId 插件ID
     * @param plugin 插件实例
     */
    public void executePluginPermissionMigration(String pluginId, ThingConnectPlugin plugin) {
        if (!migrationEnabled) {
            log.info("插件权限迁移已禁用，跳过插件 {} 的权限脚本执行", pluginId);
            return;
        }

        log.info("开始执行插件 {} 的权限数据库迁移，迁移功能启用状态: {}, 表前缀: {}", 
                pluginId, migrationEnabled, tablePrefix);

        try {
            // 获取数据库类型
            String databaseType = getDatabaseType();
            log.info("插件 {} 检测到数据库类型: {}", pluginId, databaseType);

            // 判断运行模式
            boolean isDevelopment = isDevelopment();
            log.info("插件 {} 运行模式: {} (配置值: {})", pluginId, isDevelopment ? "开发模式" : "生产模式", runtimeMode);

            String migrationLocation;
            boolean hasScripts;

            if (isDevelopment) {
                // 开发模式：使用classpath路径
                migrationLocation = buildPermissionMigrationLocation(pluginId, databaseType);
                log.info("插件 {} [开发模式] 构建的权限SQL脚本路径: {}", pluginId, migrationLocation);
                hasScripts = hasPermissionMigrationScripts(migrationLocation);
            } else {
                // 生产模式：使用文件系统路径
                String pluginWorkspace = plugin.getResourcesFolder().toString();
                log.info("插件 {} [生产模式] 的工作目录: {}", pluginId, pluginWorkspace);
                migrationLocation = buildPermissionMigrationLocationFromFileSystem(pluginId, databaseType, pluginWorkspace);
                log.info("插件 {} [生产模式] 构建的权限SQL脚本路径: {}", pluginId, migrationLocation);
                hasScripts = hasPermissionMigrationScriptsInFileSystem(migrationLocation);
            }

            // 检查是否存在权限SQL脚本
            if (!hasScripts) {
                log.warn("插件 {} 在路径 {} 没有找到权限SQL迁移脚本，跳过权限数据库迁移", pluginId, migrationLocation);
                return;
            }
            log.info("插件 {} 在路径 {} 找到权限SQL迁移脚本", pluginId, migrationLocation);

            // 创建插件专用的权限迁移历史表名
            String schemaHistoryTable = tablePrefix + pluginId.replace("-", "_") + "_schema_history";
            log.info("插件 {} 使用的Flyway历史表名: {}", pluginId, schemaHistoryTable);

            // 配置并执行Flyway迁移
            String flywayLocation = isDevelopment ? migrationLocation : ("filesystem:" + migrationLocation);
            log.info("插件 {} 开始配置Flyway，运行模式: {}, 迁移路径: {}, Flyway路径: {}, 历史表: {}", 
                    pluginId, isDevelopment ? "开发模式" : "生产模式", migrationLocation, flywayLocation, schemaHistoryTable);
            
            Flyway flyway = Flyway.configure()
                    .dataSource(dataSource)
                    .locations(flywayLocation)
                    .baselineOnMigrate(true)
                    .baselineVersion("1.0")
                    .table(schemaHistoryTable)
                    .validateOnMigrate(true)
                    .outOfOrder(false)
                    .load();

            // 执行迁移
            log.info("插件 {} 开始执行Flyway迁移", pluginId);
//            var result = flyway.migrate();
//            log.info("插件 {} Flyway迁移完成，执行了 {} 个脚本", pluginId, result.migrationsExecuted);

            log.info("插件 {} 权限数据库迁移完成", pluginId);
        } catch (SQLException e) {
            log.error("插件 {} 权限数据库迁移失败 - 数据库连接或SQL执行错误: {}", pluginId, e.getMessage(), e);
            throw new RuntimeException("插件权限数据库迁移失败 - 数据库错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("插件 {} 权限数据库迁移失败 - 未知错误: {}", pluginId, e.getMessage(), e);
            throw new RuntimeException("插件权限数据库迁移失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理插件权限相关数据
     * @param pluginId 插件ID
     */
    public void cleanupPluginPermissionData(String pluginId) {
        if (!cleanupEnabled || !migrationEnabled) {
            log.info("插件权限数据清理已禁用，跳过插件 {} 的权限数据清理", pluginId);
            return;
        }

        log.info("开始清理插件 {} 的权限数据库数据，清理功能启用状态: {}", pluginId, cleanupEnabled);
        
        try {
            // 1. 删除角色权限映射表中的数据
            int rolePermissionCount = jdbcTemplate.update(
                "DELETE FROM tcs_role_permission_map WHERE PluginId = ?", 
                pluginId
            );
            log.info("删除插件 {} 的角色权限映射 {} 条", pluginId, rolePermissionCount);
            
            // 2. 删除权限代码表中的数据
            int authCodeCount = jdbcTemplate.update(
                "DELETE FROM tcs_auth_code WHERE plugin_id = ?", 
                pluginId
            );
            log.info("删除插件 {} 的权限代码 {} 条", pluginId, authCodeCount);
            
            // 3. 删除菜单项表中的数据
            int menuItemCount = jdbcTemplate.update(
                "DELETE FROM tcs_menu_item WHERE PluginId = ?", 
                pluginId
            );
            log.info("删除插件 {} 的菜单项 {} 条", pluginId, menuItemCount);
            
            // 4. 清理权限相关的Flyway历史记录
            cleanupPermissionFlywayHistory(pluginId);
            
            log.info("插件 {} 权限数据库数据清理完成", pluginId);
        } catch (Exception e) {
            log.error("插件 {} 权限数据库数据清理失败", pluginId, e);
            // 为了不阻止插件卸载，这里选择不抛出异常
        }
    }

    /**
     * 获取数据库类型
     * @return 数据库类型字符串
     */
    private String getDatabaseType() throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            String productName = metaData.getDatabaseProductName().toLowerCase();
            String productVersion = metaData.getDatabaseProductVersion();
            String driverName = metaData.getDriverName();
            
            log.debug("数据库信息: 产品名称={}, 版本={}, 驱动={}", productName, productVersion, driverName);

            String databaseType;
            if (productName.contains("postgresql")) {
                databaseType = "postgresql";
            } else if (productName.contains("mysql")) {
                databaseType = "mysql";
            } else if (productName.contains("h2")) {
                databaseType = "h2";
            } else if (productName.contains("opengauss")) {
                databaseType = "opengauss";
            } else {
                log.warn("未知的数据库类型: {}, 使用默认的h2配置", productName);
                databaseType = "h2";
            }
            
            log.debug("识别的数据库类型: {} (基于产品名称: {})", databaseType, productName);
            return databaseType;
        }
    }

    /**
     * 构建权限相关的迁移脚本路径
     * @param pluginId 插件ID
     * @param databaseType 数据库类型
     * @return 权限迁移脚本路径
     */
    private String buildPermissionMigrationLocation(String pluginId, String databaseType) {
        // 权限相关的hub脚本路径格式: classpath:db/{plugin-id}/hub/{database-type}
        String migrationLocation = String.format("classpath:db/%s/hub/%s", pluginId, databaseType);
        log.debug("为插件 {} 构建权限迁移路径: pluginId={}, databaseType={}, 最终路径={}", 
                pluginId, pluginId, databaseType, migrationLocation);
        return migrationLocation;
    }

    /**
     * 检查是否存在权限迁移脚本
     * @param migrationLocation 迁移脚本路径
     * @return 是否存在脚本
     */
    private boolean hasPermissionMigrationScripts(String migrationLocation) {
        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            String searchPattern = migrationLocation.replace("classpath:", "classpath*:") + "/**/*.sql";
            log.info("使用搜索模式查找SQL脚本: {}", searchPattern);
            
            Resource[] resources = resolver.getResources(searchPattern);
            log.info("在路径 {} 找到 {} 个SQL脚本文件", migrationLocation, resources.length);
            
            if (resources.length > 0) {
                log.info("找到的SQL脚本文件列表:");
                for (Resource resource : resources) {
                    try {
                        log.info("  - {}", resource.getURI());
                    } catch (IOException ex) {
                        log.info("  - {} (无法获取URI: {})", resource.getDescription(), ex.getMessage());
                    }
                }
            }
            
            return resources.length > 0;
        } catch (IOException e) {
            log.error("检查权限迁移脚本时出错: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 清理插件权限相关的Flyway历史记录
     * @param pluginId 插件ID
     */
    private void cleanupPermissionFlywayHistory(String pluginId) {
        try {
            // 创建插件专用的权限迁移历史表名
            String schemaHistoryTable = tablePrefix + pluginId.replace("-", "_") + "_schema_history";
            log.info("准备清理插件 {} 的权限迁移Flyway历史表: {}", pluginId, schemaHistoryTable);
            
            // 删除整个权限迁移历史表，确保重新安装时权限脚本重新执行
            String dropTableSql = "DROP TABLE IF EXISTS " + schemaHistoryTable;
            log.debug("执行清理SQL: {}", dropTableSql);
            
            jdbcTemplate.execute(dropTableSql);
            log.info("成功清理插件 {} 的权限迁移Flyway历史表: {}", pluginId, schemaHistoryTable);
            
        } catch (Exception e) {
            log.error("清理插件 {} 的权限迁移Flyway历史表失败: {}", pluginId, e.getMessage(), e);
        }
    }

    /**
     * 构建权限相关的迁移脚本路径（文件系统路径）
     * @param pluginId 插件ID
     * @param databaseType 数据库类型
     * @param pluginWorkspace 插件工作目录
     * @return 权限迁移脚本路径
     */
    private String buildPermissionMigrationLocationFromFileSystem(String pluginId, String databaseType, String pluginWorkspace) {
        // 权限相关的hub脚本路径格式: {plugin-workspace}/db/{plugin-id}/hub/{database-type}
        String migrationLocation = String.format("%s/db/%s/hub/%s", pluginWorkspace, pluginId, databaseType);
        log.debug("为插件 {} 构建文件系统权限迁移路径: pluginWorkspace={}, pluginId={}, databaseType={}, 最终路径={}", 
                pluginId, pluginWorkspace, pluginId, databaseType, migrationLocation);
        return migrationLocation;
    }

    /**
     * 检查文件系统中是否存在权限迁移脚本
     * @param migrationLocation 迁移脚本路径
     * @return 是否存在脚本
     */
    private boolean hasPermissionMigrationScriptsInFileSystem(String migrationLocation) {
        try {
            Path dirPath = Paths.get(migrationLocation);
            log.info("检查文件系统路径是否存在: {}", dirPath.toAbsolutePath());
            
            if (!Files.exists(dirPath)) {
                log.warn("权限迁移脚本目录不存在: {}", dirPath.toAbsolutePath());
                return false;
            }
            
            if (!Files.isDirectory(dirPath)) {
                log.warn("权限迁移脚本路径不是目录: {}", dirPath.toAbsolutePath());
                return false;
            }
            
            // 查找所有.sql文件
            try (Stream<Path> files = Files.walk(dirPath)) {
                List<Path> sqlFiles = files
                    .filter(path -> path.toString().toLowerCase().endsWith(".sql"))
                    .toList();
                
                log.info("在路径 {} 找到 {} 个SQL脚本文件", migrationLocation, sqlFiles.size());
                
                if (!sqlFiles.isEmpty()) {
                    log.info("找到的SQL脚本文件列表:");
                    for (Path sqlFile : sqlFiles) {
                        log.info("  - {}", sqlFile.toAbsolutePath());
                    }
                }
                
                return !sqlFiles.isEmpty();
            }
        } catch (Exception e) {
            log.error("检查文件系统权限迁移脚本时出错: {}", e.getMessage(), e);
            return false;
        }
    }
}