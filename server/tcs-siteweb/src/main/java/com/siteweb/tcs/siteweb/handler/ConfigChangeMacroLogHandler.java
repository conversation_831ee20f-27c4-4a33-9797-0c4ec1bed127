package com.siteweb.tcs.siteweb.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.util.AnnotationUtils;
import com.siteweb.tcs.siteweb.util.TableExistenceChecker;
import lombok.extern.slf4j.Slf4j;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import com.siteweb.tcs.siteweb.annotation.ConfigId;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.enums.ChangeOperatorEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 配置变更宏日志处理器
 * 读取实体类中的ChangeSource以及ConfigId标签，并组成插入记录写入数据库中
 */
@Component
@Slf4j
public class ConfigChangeMacroLogHandler extends ObjectChangeHandlerAdapter {

    @Autowired
    private IConfigChangeDefineService configChangeDefineService;

    @Autowired
    private IConfigChangeMicroLogService configChangeMicroLogService;

    @Autowired
    private IConfigChangeMacroLogService configChangeMacroLogService;

    @Autowired
    private IConfigChangeMapService configChangeMapService;
    @Autowired
    private TableExistenceChecker tableExistenceChecker;

    // 注册的实体类型列表
    @Override
    protected List<Class<?>> doRegisterHandler() {
        List<Class<?>> types = List.of(
                EquipmentTemplate.class, WorkStation.class, MonitorUnit.class,
                Port.class, SamplerUnit.class, Equipment.class,
                ResourceStructure.class, Station.class, House.class
        );
        mapDataSourceToTable(types);
        return types;
    }

    private void mapDataSourceToTable(List<Class<?>> _classList) {
        for (Class<?> _class : _classList) {
            ChangeSource changeSource = AnnotationUtils.getAnnotationInHierarchy(_class, ChangeSource.class);
            if (changeSource == null) {
//                throw new BusinessException(String.format("类型{%s}必须实现ChangeSource注解", _class.getSimpleName()));
                continue;
            }

            ConfigId configId = AnnotationUtils.getAnnotationInHierarchy(_class, ConfigId.class);
            if (configId == null) {
//                throw new BusinessException(String.format("类型{%s}必须实现ConfigId注解", _class.getSimpleName()));
                continue;
            }
            sourceToTableMap.put(changeSource.source(), configId.value());
        }
    }

    private final HashMap<String, Integer> sourceToTableMap = new HashMap<>();


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onCreate(ChangeRecord changeRecord) {
        handlerEvent(changeRecord, ChangeOperatorEnum.CREATE);
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        handlerEvent(changeRecord, ChangeOperatorEnum.DELETE);
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        handlerEvent(changeRecord, ChangeOperatorEnum.UPDATE);
    }


    private void handlerEvent(ChangeRecord changeRecord, ChangeOperatorEnum delete) {
        if(tableExistenceChecker.isTableExists("tbl_configchangedefine")){
            return;
        }
        String dataSource = changeRecord.getDataSource();
        Integer configId = sourceToTableMap.get(dataSource);
        if (configId == null) return;
        ConfigChangeDefine configChangeDefine = configChangeDefineService.findByConfigId(configId);
        if (configChangeDefine == null) {
            log.warn("tableName:{}  TBL_ConfigChangeDefine表中没有 tableName表的定义", dataSource);
            return;
        }
        String objectId = getObjectId(new String(changeRecord.getMessage().getPayload()), configChangeDefine.getIdDefine());
        configChangeLog(objectId, configChangeDefine.getConfigId(), delete);
    }

    /**
     * 对应存储过程【PBL_ConfigChangeLog】
     * 记录与配置修改相关的日志
     */
    public void configChangeLog(String objectId, Integer configId, ChangeOperatorEnum changeOperatorEnum) {
        if(!tableExistenceChecker.isTableExists("tbl_configchangemacrolog")){
            return;
        }
        configChangeMicroLogService.configChangeLog(objectId, configId, changeOperatorEnum.getValue());
        ConfigChangeMap tblConfigChangeMap = configChangeMapService.findByConfigIdAndEditType(configId, changeOperatorEnum.getValue());
        //不存在映射关系直接返回
        if (Objects.isNull(tblConfigChangeMap)) {
            log.warn("configId:{} editType:{} 在表TBL_ConfigChangeMap中没有映射关系", configId, changeOperatorEnum.getValue());
            return;
        }
        String[] objectIdArray = objectId.split("\\.");
        String[] idConvertRuleArray = tblConfigChangeMap.getIdConvertRule().split("\\.");
        String macroObjectId = Arrays.stream(idConvertRuleArray)
                .mapToInt(Integer::parseInt)
                .filter(index -> index >= 1 && index <= objectIdArray.length) // 确保索引在数组范围内
                .mapToObj(index -> objectIdArray[index - 1])
                .collect(Collectors.joining("."));
    }

    /**
     * 通过
     *
     * @param message  json序列化过的实体类
     * @param idDefine 申明的主键值【很多联合主键】
     * @return {@link String}
     */
    private String getObjectId(String message, String idDefine) {
        //统一转换成小写，避免大小写不一致获取不到数据
        message = message.toLowerCase();
        idDefine = idDefine.toLowerCase();
        JSONObject body = JSONUtil.parseObj(JSONUtil.parseObj(message).get("body"));
        String[] primaryKeyFields = idDefine.split("\\.");
        StringJoiner primaryKeyValueJoin = new StringJoiner(".");
        for (String primaryKeyField : primaryKeyFields) {
            Object primaryKeyValue = body.get(primaryKeyField);
            primaryKeyValueJoin.add(String.valueOf(primaryKeyValue));
        }
        return primaryKeyValueJoin.toString();
    }
}
