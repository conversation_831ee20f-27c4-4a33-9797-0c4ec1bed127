package com.siteweb.tcs.s6.access.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.s6.access.dal.entity.DeviceMap;

import java.util.List;

/**
 * 设备映射服务接口
 */
public interface IDeviceMapService extends IService<DeviceMap> {

    /**
     * 根据网关ID查询
     * @param gatewayId 网关ID
     * @return 列表
     */
    List<DeviceMap> getByGatewayId(Long gatewayId);

    /**
     * 根据设备ID查询
     * @param deviceId 设备ID
     * @return 设备映射
     */
    DeviceMap getByDeviceId(Long deviceId);

    /**
     * 批量保存
     * @param deviceMaps 列表
     * @return boolean
     */
    boolean saveBatch(List<DeviceMap> deviceMaps);

    /**
     * 根据复合主键删除
     * @param gatewayId 网关ID
     * @param deviceId 设备ID
     * @param northEquipmentId 北向设备ID
     * @return boolean
     */
    boolean deleteByCompositeKey(Long gatewayId, Long deviceId, Integer northEquipmentId);
    
    List<DeviceMap> getNorthEquipmentIdListByGatewayId(Long gatewayId);

    boolean deleteByGatewayId(Long gatewayId);

    Integer getNorthEquipmentIdByDeviceId(Long deviceId);

    boolean deleteByDeviceId(Long deviceId);

    List<DeviceMap> listByDeviceIdList(List<Long> deviceIdList);
}