package com.siteweb.tcs.s6.access.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.hub.dal.dto.ConfigSyncDto;
import com.siteweb.tcs.hub.dal.dto.DeviceConfigSyncDTO;
import com.siteweb.tcs.hub.dal.entity.TcsGateway;
import com.siteweb.tcs.plugin.common.message.NorthProxySyncConfigRequest;
import com.siteweb.tcs.s6.access.config.NorthS6GatewayCluster;
import com.siteweb.tcs.s6.access.dal.entity.GatewayMap;
import com.siteweb.tcs.s6.access.dal.mapper.GatewayMapMapper;
import com.siteweb.tcs.s6.access.web.service.IGatewayMapService;
import org.apache.pekko.actor.ActorRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 网关映射服务实现类
 */
@Service
public class GatewayMapServiceImpl extends ServiceImpl<GatewayMapMapper, GatewayMap> implements IGatewayMapService {

    @Resource
    private GatewayMapMapper gatewayMapMapper;

    @Autowired
    @Qualifier(value = "tcs-north-gateway-sharding")
    private ActorRef northS6GatewayCluster;



    @Override
    public GatewayMap getByGatewayId(Long gatewayId) {
        QueryWrapper<GatewayMap> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("GatewayId", gatewayId);
        return gatewayMapMapper.selectOne(queryWrapper);
    }

    @Override
    public List<GatewayMap> getByNorthMonitorUnitId(Integer northMonitorUnitId) {
        return gatewayMapMapper.selectByNorthMonitorUnitId(northMonitorUnitId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<GatewayMap> gatewayMaps) {
        if (gatewayMaps == null || gatewayMaps.isEmpty()) {
            return false;
        }
        return gatewayMapMapper.insertBatch(gatewayMaps) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByCompositeKey(Long gatewayId, Integer northMonitorUnitId) {
        return gatewayMapMapper.deleteByCompositeKey(gatewayId, northMonitorUnitId) > 0;
    }

    @Override
    public List<GatewayMap> getAllActive() {
        return gatewayMapMapper.selectAllActive();
    }

    @Override
    public boolean deleteByGatewayId(Long gatewayId) {
        QueryWrapper<GatewayMap> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("GatewayId", gatewayId);
        return gatewayMapMapper.delete(queryWrapper) > 0;
    }

    @Override
    public List<GatewayMap> getGatewayMapWithFullRelationsByGatewayId(Long gatewayId) {
        return gatewayMapMapper.selectGatewayMapWithFullRelationsByGatewayId(gatewayId);
    }

    @Override
    public List<GatewayMap> getByGatewayIdList(List<Long> gatewayIdList) {
        LambdaQueryWrapper<GatewayMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GatewayMap::getGatewayId,gatewayIdList);
        return gatewayMapMapper.selectList(queryWrapper);
    }

    @Override
    public void syncConfig(ConfigSyncDto configSyncDto) {
        List<Long> gatewayIdList = Optional.ofNullable(configSyncDto.getGatewayIdList())
                .orElse(Collections.emptyList());
        List<DeviceConfigSyncDTO> deviceList = Optional.ofNullable(configSyncDto.getDeviceConfigSyncDTOList())
                .orElse(Collections.emptyList());

        // 1️⃣ 建立 gatewayId -> DeviceConfigSyncDTO 的映射
        Map<Long, DeviceConfigSyncDTO> deviceMap = deviceList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(DeviceConfigSyncDTO::getGatewayId, Function.identity(), (a, b) -> a));

        // 2️⃣ 求并集（hub + device 的所有 gatewayId）
        Set<Long> allGatewayIds = new LinkedHashSet<>(gatewayIdList);
        allGatewayIds.addAll(deviceMap.keySet());

        // 3️⃣ 构造 ConfigSyncDto 并发送
        allGatewayIds.forEach(gatewayId -> {
            ConfigSyncDto dto = new ConfigSyncDto();
            if(gatewayIdList.contains(gatewayId)){
                dto.setGatewayIdList(Collections.singletonList(gatewayId));
            }
            DeviceConfigSyncDTO deviceDTO = deviceMap.get(gatewayId);
            dto.setDeviceConfigSyncDTOList(
                    deviceDTO == null ? Collections.emptyList() : Collections.singletonList(deviceDTO)
            );

            NorthProxySyncConfigRequest request = new NorthProxySyncConfigRequest();
            request.setUniqueIdentifier(String.valueOf(gatewayId));
            request.setConfigSyncDTO(dto);

            northS6GatewayCluster.tell(request, ActorRef.noSender());
        });
    }
} 