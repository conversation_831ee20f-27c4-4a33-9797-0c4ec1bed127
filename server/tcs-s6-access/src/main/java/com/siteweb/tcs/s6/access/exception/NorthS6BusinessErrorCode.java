package com.siteweb.tcs.s6.access.exception;

import com.siteweb.tcs.common.exception.code.BusinessErrorCode;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-11-03 09:35
 **/

public enum NorthS6BusinessErrorCode implements BusinessErrorCode {

    REQUEST_PARAM_ERROR("BIZ-RESOURCE-001", "资源未找到");

    private final String code;
    private final String message;

    NorthS6BusinessErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
