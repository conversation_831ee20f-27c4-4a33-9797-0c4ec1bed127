package com.siteweb.tcs.s6.access.connector.process;

import com.siteweb.stream.core.entity.StreamGraph;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.common.messages.LifeCycleEvent;
import com.siteweb.tcs.plugin.common.AbstractNorthProxy;
import com.siteweb.tcs.plugin.common.message.NorthProxySyncConfigRequest;
import com.siteweb.tcs.plugin.common.message.NorthProxySyncConfigResponse;
import com.siteweb.tcs.plugin.common.sharding.INorthProxy;
import com.siteweb.tcs.provider.AbstractLifecycleDataProvider;
import com.siteweb.tcs.provider.DataProvider;
import com.siteweb.tcs.provider.DataProviderManager;
import com.siteweb.tcs.provider.enums.ProviderType;
import com.siteweb.tcs.s6.access.connector.ConnectorDataHolder;
import com.siteweb.tcs.s6.access.dal.dto.SinkDTO;
import com.siteweb.tcs.s6.access.dal.entity.GatewayMap;
import com.siteweb.tcs.s6.access.dal.provider.GatewayMapProvider;
import com.siteweb.tcs.s6.access.web.lcm.LCMHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.Props;

import java.util.List;

/**
 * 北向S6代理Actor，处理生命周期事件
 * <p>
 * 负责处理北向接口的消息和状态
 * </p>
 */
@Slf4j
public class NorthS6GatewayProxy extends AbstractNorthProxy {

    private Long gatewayGuid;

    private SinkDTO sinkDTO;

    private GatewayMapProvider gatewayMapProvider;

    private final LCMHandler lcmHandler;

    private final StreamGraph streamGraph;

    private DataProviderManager dataProviderManager;

    private NorthS6GatewayProxy(String uniqueIdentifier,String sharkingName, SinkDTO sinkDTO,StreamGraph streamGraph){
        super(uniqueIdentifier,sharkingName);
        this.gatewayGuid = Long.parseLong(uniqueIdentifier);
        this.sinkDTO = sinkDTO;
        // 订阅通道
        gatewayMapProvider = ConnectorDataHolder.getInstance().getBean(GatewayMapProvider.class);
        // 初始化分布式发布/订阅中介器
        lcmHandler = ConnectorDataHolder.getInstance().getBean(LCMHandler.class);
        this.streamGraph = streamGraph;
        this.dataProviderManager = ConnectorDataHolder.getInstance().getBean(DataProviderManager.class);
        log.info("S6 Gateway: {} Entity is Constructor, path={}", getSelf().path().name(), getSelf().path());
    }

    /**
     * Returns the props for creating a {@link NorthS6GatewayProxy} Actor.
     *
     * @return a Props for creating a NorthS6GatewayProxy Actor
     */
    public static Props props(String uniqueIdentifier,String sharkingName,SinkDTO sinkDTO,StreamGraph streamGraph) {
        return Props.create(NorthS6GatewayProxy.class,uniqueIdentifier,sharkingName,sinkDTO,streamGraph);
    }

    @Override
    protected INorthProxy getNorthProxyEntity() {
        GatewayMap gatewayMap = gatewayMapProvider.getGatewayMapByGatewayGuid(gatewayGuid);
        return gatewayMap;
    }

    @Override
    protected void onCreateEvent(LifeCycleEvent lifeCycleEvent) {
        super.onCreateEvent(lifeCycleEvent);
        handlerLCM(lifeCycleEvent);
    }

    @Override
    protected void onUpdateEvent(LifeCycleEvent lifeCycleEvent) {
        super.onUpdateEvent(lifeCycleEvent);
        handlerLCM(lifeCycleEvent);
    }

    @Override
    protected void onDeleteEvent(LifeCycleEvent lifeCycleEvent) {
        super.onDeleteEvent(lifeCycleEvent);
        handlerLCM(lifeCycleEvent);
    }

    protected void handlerLCM(LifeCycleEvent lifeCycleEvent){
        Object eventChangeObject = lifeCycleEvent.getChangeObject();
        if(eventChangeObject instanceof GatewayConfigChangeDto cycleEvent){
            lcmHandler.handleConfigChange(cycleEvent);
        }
    }

    /**
     * 同步hub配置
     * @param northProxySyncConfigRequest
     * @return
     */
    @Override
    protected NorthProxySyncConfigResponse syncHubConfig(NorthProxySyncConfigRequest northProxySyncConfigRequest) {
        lcmHandler.handleConfigSync(northProxySyncConfigRequest.getConfigSyncDTO());
        return new NorthProxySyncConfigResponse();
    }

    @Override
    protected StreamGraph getStreamGraph() {
        return streamGraph;
    }

    @Override
    protected List<DataProvider> getBusinessDataProvider() {

        DataProvider hubRealTimeData = dataProviderManager.getOrCreateProvider(ConnectorDataHolder.getInstance().getProviderId(ProviderType.HUB_REAL_TIME_DATA), ProviderType.HUB_REAL_TIME_DATA);
        DataProvider hubRealAlarm = dataProviderManager.getOrCreateProvider(ConnectorDataHolder.getInstance().getProviderId(ProviderType.HUB_REAL_ALARM), ProviderType.HUB_REAL_ALARM);
        DataProvider hubDeviceStateChange = dataProviderManager.getOrCreateProvider(ConnectorDataHolder.getInstance().getProviderId(ProviderType.HUB_DEVICE_STATE_CHANGE), ProviderType.HUB_DEVICE_STATE_CHANGE);
        DataProvider hubGatewayStateChange = dataProviderManager.getOrCreateProvider(ConnectorDataHolder.getInstance().getProviderId(ProviderType.HUB_GATEWAY_STATE_CHANGE), ProviderType.HUB_GATEWAY_STATE_CHANGE);
        return List.of(hubRealTimeData,hubRealAlarm,hubDeviceStateChange,hubGatewayStateChange);
    }

    @Override
    protected List<DataProvider> getLifeCycleDataProvider() {
        AbstractLifecycleDataProvider hubLifeCycleEvent = (AbstractLifecycleDataProvider) dataProviderManager.getOrCreateProvider(ConnectorDataHolder.getInstance().getProviderId(ProviderType.HUB_LIFECYCLE_EVENT), ProviderType.HUB_LIFECYCLE_EVENT);
        return List.of(hubLifeCycleEvent);
    }

    @Override
    protected List<Long> getThingIdList() {
        return List.of(gatewayGuid);
    }
}