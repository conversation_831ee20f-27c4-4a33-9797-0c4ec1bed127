<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.s6.access.dal.mapper.AlarmMapMapper">
    
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.s6.access.dal.entity.AlarmMap">
        <result column="DeviceId" property="deviceId" />
        <result column="NorthEquipmentId" property="northEquipmentId" />
        <result column="AlarmId" property="alarmId" />
        <result column="NorthEventId" property="northEventId" />
        <result column="NorthEventConditionId" property="northEventConditionId" />
        <result column="Deleted" property="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        DeviceId, NorthEquipmentId, AlarmId, NorthEventId, NorthEventConditionId, Deleted
    </sql>

    <!-- 根据设备ID查询告警映射 -->
    <select id="selectByDeviceId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_alarm_map
        WHERE DeviceId = #{deviceId}
        AND Deleted = FALSE
    </select>

    <!-- 根据北向设备ID查询告警映射 -->
    <select id="selectByNorthEquipmentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_alarm_map
        WHERE NorthEquipmentId = #{northEquipmentId}
        AND Deleted = FALSE
    </select>

    <!-- 根据告警ID查询告警映射 -->
    <select id="selectByAlarmId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_alarm_map
        WHERE AlarmId = #{alarmId}
        AND Deleted = FALSE
    </select>

    <!-- 根据北向事件ID查询告警映射 -->
    <select id="selectByNorthEventId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_alarm_map
        WHERE NorthEventId = #{northEventId}
        AND Deleted = FALSE
    </select>

    <!-- 根据复合主键删除告警映射 -->
    <update id="deleteByCompositeKey">
        UPDATE tcs_alarm_map 
        SET Deleted = TRUE
        WHERE DeviceId = #{deviceId}
        AND NorthEquipmentId = #{northEquipmentId}
        AND AlarmId = #{alarmId}
        AND NorthEventId = #{northEventId}
        AND NorthEventConditionId = #{northEventConditionId}
    </update>

<!--    &lt;!&ndash; 查询所有活跃的告警映射 &ndash;&gt;-->
<!--    <select id="selectList" resultMap="BaseResultMap">-->
<!--        SELECT <include refid="Base_Column_List" />-->
<!--        FROM tcs_alarm_map-->
<!--        WHERE Deleted = FALSE-->
<!--        ORDER BY DeviceId, NorthEquipmentId, AlarmId-->
<!--    </select>-->

</mapper> 